package com.xiang.proxy.server.diagnostic;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 诊断管理器
 * 用于启动和管理各种诊断工具
 */
public class DiagnosticManager {
    private static final Logger logger = LoggerFactory.getLogger(DiagnosticManager.class);
    
    private static final DiagnosticManager INSTANCE = new DiagnosticManager();
    
    private volatile boolean started = false;
    
    private DiagnosticManager() {}
    
    public static DiagnosticManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 启动所有诊断工具
     */
    public void startDiagnostics() {
        if (started) {
            logger.info("诊断工具已经启动");
            return;
        }
        
        logger.info("启动诊断管理器");
        
        // 启动数据丢失诊断
        DataLossDiagnostic.getInstance().startDiagnostic();
        
        started = true;
        logger.info("所有诊断工具已启动");
    }
    
    /**
     * 停止所有诊断工具
     */
    public void stopDiagnostics() {
        if (!started) {
            return;
        }
        
        logger.info("停止诊断管理器");
        
        // 停止数据丢失诊断
        DataLossDiagnostic.getInstance().stopDiagnostic();
        
        started = false;
        logger.info("所有诊断工具已停止");
    }
    
    /**
     * 打印综合诊断报告
     */
    public void printComprehensiveReport() {
        logger.info("=== 综合诊断报告 ===");
        
        // 数据丢失诊断统计
        DataLossDiagnostic.DiagnosticStats stats = DataLossDiagnostic.getInstance().getStats();
        logger.info("数据传输统计:");
        logger.info("  连接创建: {}", stats.totalConnectionsCreated);
        logger.info("  数据发送: {} 字节", stats.totalDataSent);
        logger.info("  数据接收: {} 字节", stats.totalDataReceived);
        logger.info("  数据接收率: {:.2f}%", stats.getDataReceiveRate());
        
        logger.info("路由统计:");
        logger.info("  路由注册: {}", stats.routeRegistrations);
        logger.info("  路由注销: {}", stats.routeUnregistrations);
        logger.info("  路由未命中: {}", stats.routeMisses);
        logger.info("  路由未命中率: {:.2f}%", stats.getRouteMissRate());
        logger.info("  活跃路由: {}", stats.getActiveRoutes());
        
        // 问题诊断
        diagnosePotentialIssues(stats);
    }
    
    /**
     * 诊断潜在问题
     */
    private void diagnosePotentialIssues(DataLossDiagnostic.DiagnosticStats stats) {
        logger.info("=== 问题诊断 ===");
        
        boolean hasIssues = false;
        
        // 检查数据接收率
        double receiveRate = stats.getDataReceiveRate();
        if (receiveRate < 80.0 && stats.totalDataSent > 0) {
            logger.error("❌ 数据接收率过低: {:.2f}% (正常应该 > 80%)", receiveRate);
            hasIssues = true;
        } else if (receiveRate >= 80.0 && stats.totalDataSent > 0) {
            logger.info("✅ 数据接收率正常: {:.2f}%", receiveRate);
        }
        
        // 检查路由未命中率
        double missRate = stats.getRouteMissRate();
        if (missRate > 5.0 && stats.routeRegistrations > 0) {
            logger.error("❌ 路由未命中率过高: {:.2f}% (正常应该 < 5%)", missRate);
            hasIssues = true;
        } else if (stats.routeRegistrations > 0) {
            logger.info("✅ 路由未命中率正常: {:.2f}%", missRate);
        }
        
        // 检查路由泄漏
        long activeRoutes = stats.getActiveRoutes();
        if (activeRoutes > 1000) {
            logger.warn("⚠️ 活跃路由数量较多: {} (可能存在路由泄漏)", activeRoutes);
            hasIssues = true;
        } else if (activeRoutes >= 0) {
            logger.info("✅ 活跃路由数量正常: {}", activeRoutes);
        }
        
        // 检查连接与数据的比例
        if (stats.totalConnectionsCreated > 0 && stats.totalDataSent > 0) {
            double avgDataPerConnection = (double) stats.totalDataSent / stats.totalConnectionsCreated;
            if (avgDataPerConnection < 100) {
                logger.warn("⚠️ 平均每连接数据量较少: {:.2f} 字节 (可能存在连接浪费)", avgDataPerConnection);
                hasIssues = true;
            } else {
                logger.info("✅ 平均每连接数据量: {:.2f} 字节", avgDataPerConnection);
            }
        }
        
        if (!hasIssues) {
            logger.info("✅ 未发现明显问题");
        }
    }
    
    /**
     * 获取诊断建议
     */
    public void printDiagnosticSuggestions() {
        DataLossDiagnostic.DiagnosticStats stats = DataLossDiagnostic.getInstance().getStats();
        
        logger.info("=== 诊断建议 ===");
        
        double receiveRate = stats.getDataReceiveRate();
        if (receiveRate < 50.0 && stats.totalDataSent > 0) {
            logger.info("🔧 数据接收率极低，建议检查:");
            logger.info("   1. 后端服务器是否正常响应");
            logger.info("   2. 网络连接是否稳定");
            logger.info("   3. MultiplexBackendDataRouter是否正确注册");
            logger.info("   4. 会话路由表是否存在泄漏");
        } else if (receiveRate < 80.0 && stats.totalDataSent > 0) {
            logger.info("🔧 数据接收率偏低，建议检查:");
            logger.info("   1. 网络延迟或丢包情况");
            logger.info("   2. 后端服务器处理能力");
            logger.info("   3. 连接池配置是否合理");
        }
        
        double missRate = stats.getRouteMissRate();
        if (missRate > 10.0 && stats.routeRegistrations > 0) {
            logger.info("🔧 路由未命中率过高，建议检查:");
            logger.info("   1. 连接建立和路由注册的时序");
            logger.info("   2. 连接ID生成是否唯一");
            logger.info("   3. 路由注销是否及时");
        }
        
        long activeRoutes = stats.getActiveRoutes();
        if (activeRoutes > 1000) {
            logger.info("🔧 活跃路由过多，建议:");
            logger.info("   1. 检查连接关闭时是否正确注销路由");
            logger.info("   2. 添加路由清理机制");
            logger.info("   3. 监控连接生命周期");
        }
        
        if (stats.totalDataSent == 0) {
            logger.info("🔧 未检测到数据发送，建议检查:");
            logger.info("   1. 客户端是否正常发送数据");
            logger.info("   2. 诊断工具是否正确集成");
            logger.info("   3. 日志级别是否正确设置");
        }
    }
    
    /**
     * 检查是否已启动
     */
    public boolean isStarted() {
        return started;
    }
}
