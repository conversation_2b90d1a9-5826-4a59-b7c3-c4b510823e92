package com.xiang.proxy.server.outbound.impl;

import com.xiang.proxy.server.blacklist.HostBlacklist;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.diagnostic.DataLossDiagnostic;
import com.xiang.proxy.server.inbound.impl.multiplex.MultiplexBackendDataRouter;
import com.xiang.proxy.server.inbound.impl.multiplex.MultiplexSession;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import com.xiang.proxy.server.outbound.AsyncOutboundConnection;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.outbound.OutboundConfig;
import com.xiang.proxy.server.util.ConnectionKeyUtils;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollSocketChannel;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 异步TCP直连出站处理器
 * 立即返回连接对象，支持连接建立过程中的数据缓存
 */
public class AsyncTcpDirectOutboundHandler implements OutboundHandler {
    private static final Logger logger = LoggerFactory.getLogger(AsyncTcpDirectOutboundHandler.class);

    private final String outboundId;
    private final OutboundConfig config;
    private final Bootstrap bootstrapTemplate;
    private final AtomicInteger connectionCounter = new AtomicInteger(0);
    private final AtomicInteger successCounter = new AtomicInteger(0);
    private final AtomicInteger failureCounter = new AtomicInteger(0);

    // 活跃连接管理 - 替代连接池
    private final ConcurrentHashMap<String, AsyncOutboundConnection> activeConnections = new ConcurrentHashMap<>();

    // 性能监控
    private final AdvancedMetrics advancedMetrics = AdvancedMetrics.getInstance();
    private final PerformanceMetrics performanceMetrics = PerformanceMetrics.getInstance();

    public AsyncTcpDirectOutboundHandler(String outboundId, OutboundConfig config) {
        this.outboundId = outboundId;
        this.config = config;
        this.bootstrapTemplate = createBootstrapTemplate();

        logger.info("异步TCP直连出站处理器初始化完成: {}", outboundId);
    }

    /**
     * 创建Bootstrap模板
     */
    private Bootstrap createBootstrapTemplate() {
        //PlatformDependent.isWindows()
        Class<? extends SocketChannel> socketChannelClazz = NioSocketChannel.class;

        if (Epoll.isAvailable()) {
            socketChannelClazz = EpollSocketChannel.class;
        }

        Bootstrap template = new Bootstrap();
        template.channel(socketChannelClazz)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getConnectTimeout())
                .option(ChannelOption.SO_RCVBUF, config.getReceiveBufferSize())
                .option(ChannelOption.SO_SNDBUF, config.getSendBufferSize())
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK,
                        new WriteBufferWaterMark(config.getLowWaterMark(), config.getHighWaterMark()))
                .handler(new ChannelInitializer<Channel>() {
                    @Override
                    protected void initChannel(Channel ch) throws Exception {
                        // 连接建立后再设置处理器
                    }
                });

        return template;
    }

    @Override
    public CompletableFuture<OutboundConnection> connect(ProxyRequest request) {
        long startTime = System.currentTimeMillis();
        connectionCounter.incrementAndGet();

        // 记录请求和协议统计
        advancedMetrics.recordRequest();
        advancedMetrics.recordProtocolUsage("TCP");
        performanceMetrics.incrementTcpConnections();
        performanceMetrics.incrementTotalConnections();

        logger.debug("开始建立异步TCP连接: {} -> {}:{}",
                request.getRequestId(), request.getTargetHost(), request.getTargetPort());

        // 优化：尝试复用现有连接，但在队列模式下需要特殊处理
        String targetKey = ConnectionKeyUtils.generateConnectionKey(request);

        // 在队列模式下，每个会话都需要独立的连接，避免数据路由混乱
        // 检查是否为多路复用协议，如果是则不复用连接
        boolean isMultiplexProtocol = ProxyRequest.Protocol.MULTIPLEX.equals(
            request.getAttribute(ProxyRequest.Attributes.ORIGINAL_PROTOCOL));

        AsyncOutboundConnection existingConnection = null;
        if (!isMultiplexProtocol) {
            existingConnection = findReusableConnection(targetKey);
        }

        if (existingConnection != null && existingConnection.isActive()) {
            logger.debug("复用现有连接: {} -> {}:{}",
                    request.getRequestId(), request.getTargetHost(), request.getTargetPort());

            // 创建包装连接对象
            OutboundConnection wrappedConnection = OutboundConnection.builder()
                    .connectionId(existingConnection.getConnectionId())
                    .backendChannel(existingConnection.getBackendChannel())
                    .target(existingConnection.getTargetHost(), existingConnection.getTargetPort())
                    .protocol(existingConnection.getProtocol())
                    .createTime(existingConnection.getCreateTime())
                    .attribute(AsyncOutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                    .attribute(AsyncOutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                    .attribute(AsyncOutboundConnection.Attributes.REQUEST_SERIAL_ID, request.getRequestId())
                    .attribute(AsyncOutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                    .attribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION, existingConnection)
                    .build();

            return CompletableFuture.completedFuture(wrappedConnection);
        }

        // 立即创建AsyncOutboundConnection对象
        AsyncOutboundConnection asyncConnection = AsyncOutboundConnection.builder()
                .target(request.getTargetHost(), request.getTargetPort())
                .protocol(request.getProtocol())
                .createTime(startTime)
                .attribute(AsyncOutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                .attribute(AsyncOutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                .attribute(AsyncOutboundConnection.Attributes.REQUEST_SERIAL_ID, request.getRequestId())
                .attribute(AsyncOutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                .build();

        // 标记连接正在建立
        asyncConnection.markConnecting();

        // 同步地将连接添加到活跃连接管理，确保并发安全
        synchronized (activeConnections) {
            // 再次检查是否已有相同的连接正在建立（双重检查锁定模式）
            if (isMultiplexProtocol) {
                String sessionKey = targetKey + "_session_" + request.getSessionId();
                AsyncOutboundConnection existingSessionConnection = findConnectionBySessionKey(sessionKey);
                if (existingSessionConnection != null) {
                    logger.debug("发现相同会话的连接正在建立，复用: sessionKey={}", sessionKey);
                    // 复用现有连接
                    OutboundConnection wrappedConnection = OutboundConnection.builder()
                            .connectionId(existingSessionConnection.getConnectionId())
                            .backendChannel(existingSessionConnection.getBackendChannel())
                            .target(existingSessionConnection.getTargetHost(), existingSessionConnection.getTargetPort())
                            .protocol(existingSessionConnection.getProtocol())
                            .createTime(existingSessionConnection.getCreateTime())
                            .attribute(AsyncOutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                            .attribute(AsyncOutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                            .attribute(AsyncOutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                            .attribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION, existingSessionConnection)
                            .build();
                    return CompletableFuture.completedFuture(wrappedConnection);
                }
                // 为多路复用协议添加会话标识
                asyncConnection.setAttribute("SESSION_KEY", sessionKey);
            }

            activeConnections.put(asyncConnection.getConnectionId(), asyncConnection);
        }

        // 预注册会话路由（在连接建立前，解决时序问题）
        MultiplexSession multiplexSession = request.getAttribute(MultiplexSession.REQUEST_ATTR_KEY);
        if (multiplexSession != null) {
            MultiplexBackendDataRouter.preRegisterSessionRoute(
                asyncConnection.getConnectionId(),
                request.getSessionId(),
                request.getClientChannel(),
                multiplexSession::handleSessionError
            );
        }

        // 在后台异步建立连接
        establishConnectionAsync(asyncConnection, request, startTime);

        // 立即返回连接对象（创建OutboundConnection）
        OutboundConnection wrappedConnection = OutboundConnection.builder()
                .connectionId(asyncConnection.getConnectionId())
                .backendChannel(asyncConnection.getBackendChannel()) // 可能为null
                .target(asyncConnection.getTargetHost(), asyncConnection.getTargetPort())
                .protocol(asyncConnection.getProtocol())
                .createTime(startTime)
                .attribute(AsyncOutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                .attribute(AsyncOutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                .attribute(AsyncOutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                .attribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION, asyncConnection) // 存储异步连接引用
                .build();

        return CompletableFuture.completedFuture(wrappedConnection);
    }

    /**
     * 异步建立连接
     */
    private void establishConnectionAsync(AsyncOutboundConnection asyncConnection, ProxyRequest request, long startTime) {
        EventLoopGroup eventLoopGroup = request.getClientChannel().eventLoop();
        Bootstrap connectionBootstrap = bootstrapTemplate.clone().group(eventLoopGroup).handler(new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel ch) {
                // 使用新的后端数据路由器，支持动态会话路由
                ch.pipeline().addLast("backend-data-router", new MultiplexBackendDataRouter(asyncConnection.getConnectionId()));

                // 注意：路由信息已经在连接建立前预注册，这里不需要重复注册
                // 连接建立成功后会自动激活预注册的路由
            }
        });

        ChannelFuture connectFuture = connectionBootstrap.connect(request.getTargetHost(), request.getTargetPort());

        connectFuture.addListener((ChannelFutureListener) future -> {
            long connectTime = System.currentTimeMillis() - startTime;

            if (future.isSuccess()) {
                Channel backendChannel = future.channel();
                logger.debug("异步TCP连接建立成功: {} -> {}:{}, 耗时: {}ms",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(), connectTime);

                // 诊断工具：记录连接创建
                DataLossDiagnostic.getInstance().recordConnectionCreated(
                    asyncConnection.getConnectionId(),
                    request.getTargetHost() + ":" + request.getTargetPort());

                // 激活预注册的会话路由
                MultiplexBackendDataRouter.activateSessionRoute(asyncConnection.getConnectionId());

                // 设置后端连接
                asyncConnection.setBackendChannel(backendChannel);
                asyncConnection.setAttribute(AsyncOutboundConnection.Attributes.CONNECT_TIME, connectTime);

                // 添加连接关闭监听器，自动清理
                backendChannel.closeFuture().addListener(closeFuture -> {
                    activeConnections.remove(asyncConnection.getConnectionId());
                    logger.debug("连接已关闭并清理: {}", asyncConnection.getConnectionId());
                });

                // 记录成功指标
                successCounter.incrementAndGet();
                advancedMetrics.recordLatency("tcp_connect", connectTime);
                advancedMetrics.recordConnectionQuality(request.getTargetHost(), true, connectTime);
                advancedMetrics.recordResponse();

                // 记录主机连接成功，清除黑名单记录
                HostBlacklist.getInstance().recordSuccess(request.getTargetHost());

            } else {
                logger.warn("异步TCP连接建立失败: {} -> {}:{}, 耗时: {}ms",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(),
                        connectTime, future.cause());

                // 清理预注册的路由
                MultiplexBackendDataRouter.cleanupPreRegisteredRoute(asyncConnection.getConnectionId());

                // 设置连接失败
                asyncConnection.setConnectionFailed(future.cause());

                // 从活跃连接中移除失败的连接
                activeConnections.remove(asyncConnection.getConnectionId());

                // 记录失败指标
                failureCounter.incrementAndGet();
                advancedMetrics.recordConnectionQuality(request.getTargetHost(), false, connectTime);
                advancedMetrics.recordError("tcp_connect_failed");

                // 增强错误处理：根据错误类型进行分类统计
                String errorType = classifyConnectionError(future.cause());
                advancedMetrics.recordError("tcp_connect_" + errorType);

                // 如果是网络相关错误，记录额外的连接质量信息
                if (isNetworkError(future.cause())) {
                    advancedMetrics.recordConnectionQuality(request.getTargetHost(), false, connectTime);
                }

                // 记录主机连接失败，用于黑名单管理
                HostBlacklist.getInstance().recordFailure(request.getTargetHost());
            }
        });
    }

    @Override
    public CompletableFuture<Void> sendData(OutboundConnection connection, ByteBuf data) {
        // 检查是否是异步连接
        AsyncOutboundConnection asyncConnection = connection.getAttribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION);
        if (asyncConnection != null) {
            return asyncConnection.sendData(data);
        }

        // 兼容原有的OutboundConnection
        Channel channel = connection.getBackendChannel();
        if (channel == null || !channel.isActive()) {
            return CompletableFuture.failedFuture(new IllegalStateException("连接不可用"));
        }

        CompletableFuture<Void> future = new CompletableFuture<>();
        channel.writeAndFlush(data).addListener(f -> {
            if (f.isSuccess()) {
                future.complete(null);
            } else {
                future.completeExceptionally(f.cause());
            }
        });

        return future;
    }

    @Override
    public CompletableFuture<Void> closeConnection(OutboundConnection connection) {
        // 检查是否是异步连接
        AsyncOutboundConnection asyncConnection = connection.getAttribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION);
        if (asyncConnection != null) {
            return asyncConnection.close();
        }

        // 兼容原有的OutboundConnection
        Channel channel = connection.getBackendChannel();
        if (channel != null && channel.isActive()) {
            CompletableFuture<Void> future = new CompletableFuture<>();
            channel.close().addListener(f -> {
                if (f.isSuccess()) {
                    future.complete(null);
                } else {
                    future.completeExceptionally(f.cause());
                }
            });
            return future;
        }

        return CompletableFuture.completedFuture(null);
    }

    @Override
    public String getOutboundId() {
        return outboundId;
    }

    @Override
    public OutboundConfig getConfig() {
        return config;
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public String getType() {
        return "AsyncTcpDirect";
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    /**
     * 获取连接统计信息
     */
    public String getStats() {
        return String.format("AsyncTcpDirectOutbound{id='%s', total=%d, success=%d, failure=%d, active=%d, successRate=%.2f%%}",
                outboundId, connectionCounter.get(), successCounter.get(), failureCounter.get(),
                activeConnections.size(),
                connectionCounter.get() > 0 ? (double) successCounter.get() / connectionCounter.get() * 100.0 : 0.0);
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return activeConnections.size();
    }

    /**
     * 查找可复用的连接
     */
    private AsyncOutboundConnection findReusableConnection(String targetKey) {
        long currentTime = System.currentTimeMillis();

        for (AsyncOutboundConnection connection : activeConnections.values()) {
            String connTargetKey = ConnectionKeyUtils.generateConnectionKey(connection.getTargetHost(), connection.getTargetPort()
                    , connection.getProtocol(), connection.getAttribute(AsyncOutboundConnection.Attributes.REQUEST_SERIAL_ID));

            // 检查目标是否匹配且连接健康
            if (targetKey.equals(connTargetKey) &&
                    connection.isActive() &&
                    connection.isConnected() &&
                    (currentTime - connection.getLastActiveTime()) < 60000) { // 1分钟内活跃

                logger.debug("找到可复用连接: {} -> {}", connection.getConnectionId(), targetKey);
                return connection;
            }
        }

        return null;
    }

    /**
     * 根据会话键查找连接
     */
    private AsyncOutboundConnection findConnectionBySessionKey(String sessionKey) {
        return activeConnections.values().stream()
                .filter(conn -> sessionKey.equals(conn.getAttribute("SESSION_KEY")))
                .findFirst()
                .orElse(null);
    }

    /**
     * 清理不活跃的连接
     */
    public void cleanupInactiveConnections() {
        long currentTime = System.currentTimeMillis();
        AtomicInteger cleanedCount = new AtomicInteger(0);

        activeConnections.entrySet().removeIf(entry -> {
            AsyncOutboundConnection connection = entry.getValue();
            // 清理超过5分钟未活跃的连接
            if (currentTime - connection.getLastActiveTime() > 300000) {
                connection.close();
                cleanedCount.incrementAndGet();
                return true;
            }
            return false;
        });

        int cleaned = cleanedCount.get();
        if (cleaned > 0) {
            logger.info("清理了 {} 个不活跃连接", cleaned);
        }
    }

    /**
     * 关闭所有活跃连接
     */
    public void closeAllConnections() {
        logger.info("关闭所有活跃连接，数量: {}", activeConnections.size());

        activeConnections.values().forEach(connection -> {
            try {
                connection.close();
            } catch (Exception e) {
                logger.warn("关闭连接时发生异常: {}", connection.getConnectionId(), e);
            }
        });

        activeConnections.clear();
    }

    @Override
    public void destroy() {
        closeAllConnections();
        logger.info("AsyncTcpDirectOutboundHandler已销毁: {}", outboundId);
    }

    /**
     * 分类连接错误类型
     */
    private String classifyConnectionError(Throwable cause) {
        if (cause == null) return "unknown";

        String message = cause.getMessage();
        if (message == null) message = cause.getClass().getSimpleName();

        if (message.contains("timeout") || message.contains("Timeout")) {
            return "timeout";
        } else if (message.contains("refused") || message.contains("Refused")) {
            return "refused";
        } else if (message.contains("unreachable") || message.contains("Unreachable")) {
            return "unreachable";
        } else if (message.contains("reset") || message.contains("Reset")) {
            return "reset";
        } else {
            return "other";
        }
    }

    /**
     * 判断是否为网络相关错误
     */
    private boolean isNetworkError(Throwable cause) {
        if (cause == null) return false;

        String message = cause.getMessage();
        if (message == null) return false;

        return message.contains("timeout") ||
                message.contains("refused") ||
                message.contains("unreachable") ||
                message.contains("reset");
    }
}
