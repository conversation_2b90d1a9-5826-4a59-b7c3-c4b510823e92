package com.proxy.client.queue;

import com.proxy.client.protocol.MultiplexProtocol;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据包队列管理器
 * 在inbound组件和ConnectionManager之间提供缓冲和解耦
 */
public class PacketQueue {
    private static final Logger logger = LoggerFactory.getLogger(PacketQueue.class);
    
    // 队列配置
    private static final int DEFAULT_QUEUE_CAPACITY = 10000;
    private static final int DEFAULT_BATCH_SIZE = 100;
    private static final long DEFAULT_FLUSH_INTERVAL_MS = 10;
    private static final int DEFAULT_RETRY_ATTEMPTS = 3;
    private static final long DEFAULT_RETRY_DELAY_MS = 1000;
    
    // 队列和线程池
    private final BlockingQueue<QueuedPacket> packetQueue;
    private final ScheduledExecutorService scheduler;
    private final ExecutorService processingExecutor;
    
    // 状态管理
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicLong enqueuedCount = new AtomicLong(0);
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong droppedCount = new AtomicLong(0);
    
    // 配置参数
    private final int queueCapacity;
    private final int batchSize;
    private final long flushIntervalMs;
    private final int retryAttempts;
    private final long retryDelayMs;
    
    // 数据包发送接口
    private volatile PacketSender packetSender;
    
    public PacketQueue() {
        this(DEFAULT_QUEUE_CAPACITY, DEFAULT_BATCH_SIZE, DEFAULT_FLUSH_INTERVAL_MS, 
             DEFAULT_RETRY_ATTEMPTS, DEFAULT_RETRY_DELAY_MS);
    }
    
    public PacketQueue(int queueCapacity, int batchSize, long flushIntervalMs, 
                      int retryAttempts, long retryDelayMs) {
        this.queueCapacity = queueCapacity;
        this.batchSize = batchSize;
        this.flushIntervalMs = flushIntervalMs;
        this.retryAttempts = retryAttempts;
        this.retryDelayMs = retryDelayMs;
        
        // 使用有界队列防止内存溢出
        this.packetQueue = new ArrayBlockingQueue<>(queueCapacity);
        
        // 创建线程池
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "PacketQueue-Scheduler");
            t.setDaemon(true);
            return t;
        });
        
        this.processingExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "PacketQueue-Processor");
            t.setDaemon(true);
            return t;
        });
        
        logger.info("PacketQueue初始化完成 - 容量: {}, 批处理大小: {}, 刷新间隔: {}ms", 
                   queueCapacity, batchSize, flushIntervalMs);
    }
    
    /**
     * 启动队列处理
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            logger.info("启动PacketQueue处理器");
            
            // 启动批处理任务
            scheduler.scheduleWithFixedDelay(this::processBatch, 
                flushIntervalMs, flushIntervalMs, TimeUnit.MILLISECONDS);
            
            // 启动连续处理任务
            processingExecutor.submit(this::processQueue);
        }
    }
    
    /**
     * 停止队列处理
     */
    public void stop() {
        if (running.compareAndSet(true, false)) {
            logger.info("停止PacketQueue处理器");
            
            // 处理剩余数据包
            processBatch();
            
            // 关闭线程池
            scheduler.shutdown();
            processingExecutor.shutdown();
            
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                if (!processingExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    processingExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduler.shutdownNow();
                processingExecutor.shutdownNow();
            }
            
            logger.info("PacketQueue已停止 - 入队: {}, 处理: {}, 丢弃: {}", 
                       enqueuedCount.get(), processedCount.get(), droppedCount.get());
        }
    }
    
    /**
     * 设置数据包发送器
     */
    public void setPacketSender(PacketSender sender) {
        this.packetSender = sender;
        logger.debug("设置PacketSender: {}", sender != null ? sender.getClass().getSimpleName() : "null");
    }
    
    /**
     * 入队数据包
     */
    public boolean enqueue(MultiplexProtocol.Packet packet) {
        return enqueue(packet, PacketPriority.NORMAL);
    }
    
    /**
     * 入队数据包（带优先级）
     */
    public boolean enqueue(MultiplexProtocol.Packet packet, PacketPriority priority) {
        if (!running.get()) {
            logger.warn("PacketQueue未运行，丢弃数据包: {}", packet);
            droppedCount.incrementAndGet();
            return false;
        }
        
        QueuedPacket queuedPacket = new QueuedPacket(packet, priority, System.currentTimeMillis());
        
        boolean success = packetQueue.offer(queuedPacket);
        if (success) {
            enqueuedCount.incrementAndGet();
            logger.debug("数据包入队成功: {} (队列大小: {})", packet, packetQueue.size());
        } else {
            droppedCount.incrementAndGet();
            logger.warn("队列已满，丢弃数据包: {} (队列大小: {})", packet, packetQueue.size());
        }
        
        return success;
    }
    
    /**
     * 连续处理队列
     */
    private void processQueue() {
        logger.info("开始连续处理队列");
        
        while (running.get()) {
            try {
                QueuedPacket queuedPacket = packetQueue.poll(1, TimeUnit.MILLISECONDS);
                if (queuedPacket != null) {
                    processPacket(queuedPacket);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                logger.error("处理队列时发生异常", e);
            }
        }
        
        logger.info("队列处理线程退出");
    }
    
    /**
     * 批处理数据包
     */
    private void processBatch() {
        if (!running.get() || packetQueue.isEmpty()) {
            return;
        }
        
        int processed = 0;
        while (processed < batchSize && !packetQueue.isEmpty()) {
            QueuedPacket queuedPacket = packetQueue.poll();
            if (queuedPacket != null) {
                processPacket(queuedPacket);
                processed++;
            }
        }
        
        if (processed > 0) {
            logger.debug("批处理完成，处理数据包数量: {}", processed);
        }
    }
    
    /**
     * 处理单个数据包
     */
    private void processPacket(QueuedPacket queuedPacket) {
        PacketSender sender = this.packetSender;
        if (sender == null) {
            // 没有发送器，重新入队等待
            if (queuedPacket.getRetryCount() < retryAttempts) {
                queuedPacket.incrementRetry();
                
                // 延迟重试
                scheduler.schedule(() -> {
                    if (running.get()) {
                        packetQueue.offer(queuedPacket);
                    }
                }, retryDelayMs, TimeUnit.MILLISECONDS);
                
                logger.debug("PacketSender未就绪，延迟重试: {} (重试次数: {})", 
                           queuedPacket.getPacket(), queuedPacket.getRetryCount());
            } else {
                droppedCount.incrementAndGet();
                logger.warn("PacketSender未就绪且重试次数已达上限，丢弃数据包: {}", queuedPacket.getPacket());
            }
            return;
        }
        
        try {
            boolean success = sender.sendPacket(queuedPacket.getPacket());
            if (success) {
                processedCount.incrementAndGet();
                logger.debug("数据包发送成功: {}", queuedPacket.getPacket());
            } else {
                // 发送失败，重试
                if (queuedPacket.getRetryCount() < retryAttempts) {
                    queuedPacket.incrementRetry();
                    
                    scheduler.schedule(() -> {
                        if (running.get()) {
                            packetQueue.offer(queuedPacket);
                        }
                    }, retryDelayMs, TimeUnit.MILLISECONDS);
                    
                    logger.debug("数据包发送失败，延迟重试: {} (重试次数: {})", 
                               queuedPacket.getPacket(), queuedPacket.getRetryCount());
                } else {
                    droppedCount.incrementAndGet();
                    logger.warn("数据包发送失败且重试次数已达上限，丢弃: {}", queuedPacket.getPacket());
                }
            }
        } catch (Exception e) {
            logger.error("发送数据包时发生异常: {}", queuedPacket.getPacket(), e);
            droppedCount.incrementAndGet();
        }
    }
    
    /**
     * 获取队列统计信息
     */
    public QueueStats getStats() {
        return new QueueStats(
            packetQueue.size(),
            enqueuedCount.get(),
            processedCount.get(),
            droppedCount.get(),
            running.get()
        );
    }
    
    /**
     * 数据包发送接口
     */
    public interface PacketSender {
        /**
         * 发送数据包
         * @param packet 要发送的数据包
         * @return true表示发送成功，false表示发送失败
         */
        boolean sendPacket(MultiplexProtocol.Packet packet);
    }
    
    /**
     * 数据包优先级
     */
    public enum PacketPriority {
        HIGH(3),    // 高优先级（认证、心跳等）
        NORMAL(2),  // 普通优先级（数据传输）
        LOW(1);     // 低优先级（统计等）
        
        private final int level;
        
        PacketPriority(int level) {
            this.level = level;
        }
        
        public int getLevel() {
            return level;
        }
    }
    
    /**
     * 队列中的数据包包装类
     */
    private static class QueuedPacket {
        private final MultiplexProtocol.Packet packet;
        private final PacketPriority priority;
        private final long enqueueTime;
        private int retryCount = 0;
        
        public QueuedPacket(MultiplexProtocol.Packet packet, PacketPriority priority, long enqueueTime) {
            this.packet = packet;
            this.priority = priority;
            this.enqueueTime = enqueueTime;
        }
        
        public MultiplexProtocol.Packet getPacket() {
            return packet;
        }
        
        public PacketPriority getPriority() {
            return priority;
        }
        
        public long getEnqueueTime() {
            return enqueueTime;
        }
        
        public int getRetryCount() {
            return retryCount;
        }
        
        public void incrementRetry() {
            retryCount++;
        }
    }
    
    /**
     * 队列统计信息
     */
    public static class QueueStats {
        private final int queueSize;
        private final long enqueuedCount;
        private final long processedCount;
        private final long droppedCount;
        private final boolean running;
        
        public QueueStats(int queueSize, long enqueuedCount, long processedCount, 
                         long droppedCount, boolean running) {
            this.queueSize = queueSize;
            this.enqueuedCount = enqueuedCount;
            this.processedCount = processedCount;
            this.droppedCount = droppedCount;
            this.running = running;
        }
        
        public int getQueueSize() { return queueSize; }
        public long getEnqueuedCount() { return enqueuedCount; }
        public long getProcessedCount() { return processedCount; }
        public long getDroppedCount() { return droppedCount; }
        public boolean isRunning() { return running; }
        
        @Override
        public String toString() {
            return String.format("QueueStats{queueSize=%d, enqueued=%d, processed=%d, dropped=%d, running=%s}",
                queueSize, enqueuedCount, processedCount, droppedCount, running);
        }
    }
}