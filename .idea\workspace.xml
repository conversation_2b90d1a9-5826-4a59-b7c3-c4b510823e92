<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="49980bdc-5d33-4caf-ac98-620ab2f8b564" name="Changes" comment="当前proxy-server改动有严重性能问题，大改保存下。">
      <change beforePath="$PROJECT_DIR$/.idea/sqldialects.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/sqldialects.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/configs/development/server/proxy-server.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/configs/production/server/proxy-server-v2.yml" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/configs/production/server/proxy-server-v2.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/configs/production/server/proxy-server.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/connection/ConnectionManager.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/connection/ConnectionManager.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/connection/IConnectionManager.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/connection/IConnectionManager.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/handler/MultiplexSessionHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/handler/MultiplexSessionHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/handler/MultiplexSocks5Handler.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/handler/MultiplexSocks5Handler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/inbound/impl/HttpInbound.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/inbound/impl/HttpInbound.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/protocol/MultiplexProtocol.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/protocol/MultiplexProtocol.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/queue/PacketQueue.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/queue/PacketQueue.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/queue/QueuedConnectionManager.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java/com/proxy/client/queue/QueuedConnectionManager.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/ProxyClient.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/ProxyClientManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/ProxyClientConfigManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/ProxyClientConfigManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/annotation/ConfigurationProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/binder/ConfigurationBinder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$AuthProperties$TimeoutProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$AuthProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$FilterProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$InboundItemProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$InboundProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$LegacyProperties$LegacyInboundProperties$LegacyInboundItemProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$LegacyProperties$LegacyInboundProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$LegacyProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$LocalProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$NacosProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$OnlineDataSourcesProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$PerformanceProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$ProxyProperties$ServerProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$ProxyProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$QueueProperties$MonitoringProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$QueueProperties$RetryProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$QueueProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties$SslProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/config/properties/ProxyClientProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/ConnectionManager$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/ConnectionManager$MultiplexProtocolHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/ConnectionManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/DirectConnectionHandler$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/DirectConnectionHandler$DirectConnection.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/DirectConnectionHandler$DirectConnectionChannelHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/DirectConnectionHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/DynamicConnectionManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/IConnectionManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/IConnectionManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/connection/SessionHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/discovery/NacosServiceDiscovery$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/discovery/NacosServiceDiscovery$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/discovery/NacosServiceDiscovery$ServiceChangeListener.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/discovery/NacosServiceDiscovery.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/filter/AddressFilter$ConnectionType.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/filter/AddressFilter$ConnectionType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/filter/AddressFilter$FilterMode.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/filter/AddressFilter$FilterMode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/filter/AddressFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/filter/AddressFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/filter/DefaultAddressFilter.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/handler/MultiplexSessionHandler$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/handler/MultiplexSessionHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/handler/MultiplexSocks5Handler$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/handler/MultiplexSocks5Handler$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/handler/MultiplexSocks5Handler$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/handler/MultiplexSocks5Handler$State.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/handler/MultiplexSocks5Handler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/AbstractProxyInbound.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/AbstractProxyInbound.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/ProxyInbound$InboundStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/ProxyInbound$InboundStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/ProxyInbound$ProxyProtocol.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/ProxyInbound$ProxyProtocol.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/ProxyInbound.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/ProxyInbound.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/HttpInbound$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/HttpInbound$DirectDataForwarder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/HttpInbound$HttpDirectSessionHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/HttpInbound$HttpProxyHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/HttpInbound.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/Socks5Inbound$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/Socks5Inbound$ConnectionCounterHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/inbound/impl/Socks5Inbound.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/protocol/MultiplexProtocol$Packet.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/protocol/MultiplexProtocol$Packet.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/protocol/MultiplexProtocol.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/protocol/MultiplexProtocol.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/ConnectionManagerFactory.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/PacketQueue$PacketPriority.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/PacketQueue$PacketSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/PacketQueue$QueueStats.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/PacketQueue$QueuedPacket.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/PacketQueue.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/QueueMonitor$MonitorStatus.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/QueueMonitor.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/queue/QueuedConnectionManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/ssl/ClientSslContextManager$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/ssl/ClientSslContextManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/classes/com/proxy/client/util/GeoIPUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/instrument.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/maven-archiver/pom.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/client/china-ip-ranges.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/client/proxy-client.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/server/china-ip-ranges.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/server/malicious-domains.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/server/malicious-keywords.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/server/proxy-server.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/development/server/whitelist-domains.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/china-ip-ranges.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/malicious-domains.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/malicious-keywords.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/proxy-server-high-performance.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/proxy-server-ultra-performance.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/proxy-server-v2.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/proxy-server.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/netty_multiplex_proxy/configs/production/server/whitelist-domains.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/original-proxy-client-1.0.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/proxy-client-1.0.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/proxy-client.exe" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/tmp/native-image-2598835084813052953.args" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/target/tmp/native-image-4253143610170770884.args" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/ProxyServer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/ProxyServerV2.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/ProxyServerV2.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/auth/AuthConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/auth/AuthConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/blacklist/HostBlacklist.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/blacklist/HostBlacklist.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/config/ProxyProcessorConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/config/ProxyProcessorConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/config/ProxyServerConfigManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/config/properties/ProxyServerProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/AdaptiveQueueManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/BatchProxyProcessor.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyMetrics.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyProcessorFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/core/ProxyServerInitializer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/filter/GeoLocationFilter.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/filter/GeoLocationFilter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/filter/MaliciousContentLoader.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/filter/MaliciousContentLoader.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/handler/MultiplexBackendHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/handler/MultiplexProxyHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/AbstractInboundServer.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/AbstractInboundServer.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/InboundServerConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/InboundServerConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexBackendDataHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexBackendDataHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexSession.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexSession.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/AsyncOutboundConnection.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/AsyncOutboundConnection.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/OutboundConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/OutboundConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/OutboundConnection.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/OutboundConnection.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/impl/AsyncTcpDirectOutboundHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/outbound/impl/AsyncTcpDirectOutboundHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/protocol/MultiplexProtocol.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/protocol/MultiplexProtocol.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/protocol/MultiplexProtocolDetector.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/ssl/SslContextManager.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/ssl/SslContextManager.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/util/ConnectionKeyUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/util/ConnectionKeyUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/util/GeoIPUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java/com/xiang/proxy/server/util/GeoIPUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/resources/logback.xml" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/resources/logback.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/ProxyServer$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/ProxyServer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/ProxyServerV2.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/ProxyServerV2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/auth/AuthConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/auth/AuthConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/auth/AuthManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/auth/AuthManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/blacklist/HostBlacklist$BlacklistStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/blacklist/HostBlacklist$BlacklistStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/blacklist/HostBlacklist.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/blacklist/HostBlacklist.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/cache/CacheManager$CacheEntry.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/cache/CacheManager$CacheEntry.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/cache/CacheManager$CacheStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/cache/CacheManager$CacheStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/cache/CacheManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/cache/CacheManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/ProxyProcessorConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/ProxyProcessorConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/ProxyServerConfigManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/ProxyServerV2ConfigManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/ProxyServerV2ConfigManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/binder/ConfigurationBinder.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/binder/ConfigurationBinder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$AuthProperties$TimeoutProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$AuthProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$BlacklistProperties$CacheProperties$TimeoutProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$BlacklistProperties$CacheProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$BlacklistProperties$FailureProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$BlacklistProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$GeoLocationFilterProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$MetricsProperties$ReportProperties$IntervalProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$MetricsProperties$ReportProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$MetricsProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$OnlineDataSourcesProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$PerformanceProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$PoolProperties$CleanupIntervalProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$PoolProperties$IdleTimeoutProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$PoolProperties$MaxConnectionsProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$PoolProperties$MaxLifetimeProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$PoolProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$ServerProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties$SslProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$AuthProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$AuthProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$BlacklistProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$BlacklistProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$CacheConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$CacheConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$FailureConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$FailureConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$GeoLocationFilterProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$GeoLocationFilterProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$GlobalProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$GlobalProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$InboundProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$InboundProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$MaxConnectionsConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$MaxConnectionsConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$MetricsProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$MetricsProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$OnlineDataSourcesProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$OnlineDataSourcesProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$OutboundProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$OutboundProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$PerformanceProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$PerformanceProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$PoolProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$PoolProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$ReportConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$ReportConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$RouteMatcherConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$RouteMatcherConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$RouteRuleProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$RouteRuleProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$RoutingProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$RoutingProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$SslProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$SslProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$TimeoutProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties$TimeoutProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/config/properties/ProxyServerV2Properties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/AdaptiveQueueManager$LoadIndicators.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/AdaptiveQueueManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/BatchProxyProcessor$BatchStats.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/BatchProxyProcessor.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics$MetricsReport.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics$MetricsReport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics$QueueMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics$QueueMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics$RequestTimer.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics$RequestTimer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessor$QueueStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessor$QueueStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessor$QueuedRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessor$QueuedRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessor.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessorFactory$ProcessorBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessorFactory$ProcessorRecommendation.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessorFactory$ProcessorType.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyProcessorFactory.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest$Attributes.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest$Attributes.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest$Builder.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest$Builder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest$Protocol.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest$Protocol.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyResponse$Builder.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyResponse$Builder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/core/ProxyServerInitializer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandler$ExceptionResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandler$ExceptionResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandler$ExceptionType.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandler$ExceptionType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandlingConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ExceptionHandlingConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ResourceCleanupManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/exception/ResourceCleanupManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/BlockReason.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/BlockReason.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/FilterResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/FilterResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/FilterStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/FilterStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/GeoLocationFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/GeoLocationFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/MaliciousContentLoader.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/filter/MaliciousContentLoader.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/handler/MultiplexBackendHandler$SessionCleanupCallback.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/handler/MultiplexBackendHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/handler/MultiplexProxyHandler$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/handler/MultiplexProxyHandler$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/handler/MultiplexProxyHandler$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/handler/MultiplexProxyHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/AbstractInboundServer$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/AbstractInboundServer$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/AbstractInboundServer.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/AbstractInboundServer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundHandlerStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundHandlerStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServer$ServerHealthStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServer$ServerHealthStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServer.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerManager$HealthReport.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerManager$HealthReport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerManager$ManagerStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerManager$ManagerStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/InboundServerStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexBackendDataHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexBackendDataHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$MultiplexChannelHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$MultiplexChannelHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$MultiplexProtocolDetector.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$MultiplexProtocolDetector.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$MultiplexStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer$MultiplexStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexInboundServer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexSession.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/inbound/impl/multiplex/MultiplexSession.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$ConnectionQualityStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$ConnectionQualityStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$ConnectionQualityTracker.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$ConnectionQualityTracker.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$DataTransferStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$DataTransferStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$LatencyStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$LatencyStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$LatencyTracker.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$LatencyTracker.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$MemoryStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$MemoryStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$PerformanceReport.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$PerformanceReport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$PoolStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics$PoolStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/AdvancedMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/PerformanceMetrics$MetricsSnapshot.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/PerformanceMetrics$MetricsSnapshot.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/PerformanceMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/metrics/PerformanceMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/AsyncOutboundConnection$Attributes.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/AsyncOutboundConnection$Attributes.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/AsyncOutboundConnection$Builder.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/AsyncOutboundConnection$Builder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/AsyncOutboundConnection.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/AsyncOutboundConnection.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConnection$Attributes.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConnection$Attributes.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConnection$Builder.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConnection$Builder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConnection.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundConnection.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundHandler$HealthStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundHandler$HealthStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundHandlerType.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundHandlerType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/OutboundStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/AsyncTcpDirectOutboundHandler$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/AsyncTcpDirectOutboundHandler$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/AsyncTcpDirectOutboundHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/AsyncTcpDirectOutboundHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/TcpDirectOutboundHandler$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/TcpDirectOutboundHandler$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/TcpDirectOutboundHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/TcpDirectOutboundHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/UdpDirectOutboundHandler$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/UdpDirectOutboundHandler$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/UdpDirectOutboundHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/outbound/impl/UdpDirectOutboundHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/protocol/MultiplexProtocol$Packet.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/protocol/MultiplexProtocol$Packet.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/protocol/MultiplexProtocol.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/protocol/MultiplexProtocol.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/protocol/MultiplexProtocolDetector.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/DefaultRouter.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/DefaultRouter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteMatcher$Operator.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteMatcher$Operator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteMatcher$Type.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteMatcher$Type.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteMatcher.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteMatcher.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteResult$Builder.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteResult$Builder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/RouteStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/ValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/router/ValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/ssl/SslContextManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/ssl/SslContextManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/ConnectionKeyUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/ConnectionKeyUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/GeoIPUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/GeoIPUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/MemoryOptimizer$MemoryStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/MemoryOptimizer$MemoryStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/MemoryOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/MemoryOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/ThreadPoolPerformanceAnalyzer$PerformanceReport.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/ThreadPoolPerformanceAnalyzer$PerformanceReport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/ThreadPoolPerformanceAnalyzer.class" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/com/xiang/proxy/server/util/ThreadPoolPerformanceAnalyzer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/logback.xml" beforeDir="false" afterPath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/classes/logback.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/apiguardian-api-1.1.2.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/junit-jupiter-api-5.10.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/junit-jupiter-engine-5.10.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/junit-jupiter-params-5.10.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/junit-platform-commons-1.10.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/junit-platform-engine-1.10.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/logback-classic-1.2.12.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/logback-core-1.2.12.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-all-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-buffer-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-dns-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-haproxy-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-http-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-http2-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-memcache-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-mqtt-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-redis-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-smtp-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-socks-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-stomp-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-codec-xml-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-common-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-handler-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-handler-proxy-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-handler-ssl-ocsp-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-resolver-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-resolver-dns-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-resolver-dns-classes-macos-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-resolver-dns-native-macos-4.1.100.Final-osx-aarch_64.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-resolver-dns-native-macos-4.1.100.Final-osx-x86_64.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-classes-epoll-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-classes-kqueue-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-native-epoll-4.1.100.Final-linux-aarch_64.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-native-epoll-4.1.100.Final-linux-x86_64.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-native-kqueue-4.1.100.Final-osx-aarch_64.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-native-kqueue-4.1.100.Final-osx-x86_64.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-native-unix-common-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-rxtx-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-sctp-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/netty-transport-udt-4.1.100.Final.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/opentest4j-1.3.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/slf4j-api-1.7.36.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/lib/snakeyaml-2.2.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T09-33-14_200.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T09-33-24_332.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T09-53-04_688.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T09-53-53_725.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T09-54-06_274.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T10-06-29_067.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T10-06-52_190.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T10-23-20_085.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T15-03-11_865.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T15-03-22_959.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/2025-08-22T15-18-11_786.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/TEST-com.xiang.proxy.server.core.ProxyProcessorQueueTest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/TEST-com.xiang.proxy.server.inbound.impl.multiplex.MultiplexSessionConsistencyTest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/TEST-com.xiang.proxy.server.outbound.AsyncOutboundConnectionTest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/TEST-com.xiang.proxy.server.outbound.ConnectionReuseTest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/TEST-com.xiang.proxy.server.outbound.ConnectionStateIntegrationTest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/TEST-com.xiang.proxy.server.outbound.ConnectionStateTest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/TEST-com.xiang.proxy.server.outbound.impl.AsyncTcpDirectOutboundHandlerTest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/com.xiang.proxy.server.core.ProxyProcessorQueueTest.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/com.xiang.proxy.server.inbound.impl.multiplex.MultiplexSessionConsistencyTest.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/com.xiang.proxy.server.outbound.AsyncOutboundConnectionTest.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/com.xiang.proxy.server.outbound.ConnectionReuseTest.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/com.xiang.proxy.server.outbound.ConnectionStateIntegrationTest.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/com.xiang.proxy.server.outbound.ConnectionStateTest.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/target/surefire-reports/com.xiang.proxy.server.outbound.impl.AsyncTcpDirectOutboundHandlerTest.txt" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="302n8CxA6tIiSghQGFsbl4Zs7X5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.DiagnosticsUtil.executor": "Debug",
    "Application.HighPerformanceProxyServer.executor": "Debug",
    "Application.MultiplexSessionDebugger.executor": "Debug",
    "Application.OptimizedProxyServer.executor": "Debug",
    "Application.ProxyClient (1).executor": "Debug",
    "Application.ProxyClient.executor": "Debug",
    "Application.ProxyServerV2 (1).executor": "Debug",
    "Application.ProxyServerV2.executor": "Debug",
    "Application.SimpleHighPerformanceProxyServer.executor": "Debug",
    "Application.com.proxy.server.ProxyServerV2.executor": "Debug",
    "JUnit.ProxyServerIntegrationTest.executor": "Debug",
    "Maven.netty-multiplex-proxy-alicloud-parent [clean].executor": "Run",
    "Maven.netty-multiplex-proxy-alicloud-parent [compile].executor": "Run",
    "Maven.netty-multiplex-proxy-alicloud-parent [install].executor": "Run",
    "Maven.netty-multiplex-proxy-alicloud-parent [package].executor": "Run",
    "Maven.netty-multiplex-proxy-service [clean].executor": "Run",
    "Maven.netty-multiplex-proxy-service [package].executor": "Run",
    "Maven.netty-multiplex-service [clean...].executor": "Debug",
    "Maven.netty-multiplex-service [compile...].executor": "Debug",
    "Maven.netty_multiplex_proxy_alicloud [compile...].executor": "Debug",
    "Maven.proxy-client [clean].executor": "Run",
    "Maven.proxy-client [compile].executor": "Run",
    "Maven.proxy-client [install].executor": "Run",
    "Maven.proxy-server [clean].executor": "Run",
    "Maven.proxy-server [compile].executor": "Run",
    "Maven.proxy-server [install].executor": "Run",
    "Maven.proxy-server [validate].executor": "Run",
    "Maven.springcloud-alibaba-chat-service [compile...].executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.Bootstrap.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/configs/production/server",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "onboarding.tips.debug.path": "C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy_alicloud/netty-multiplex-service/src/main/java/com/xiang/Main.java",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\configs\production\server" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\netty-multiplex-proxy-service\src\main\java\com\xiang\proxy\server\core" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\configs\mysql\init" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\configs" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud" />
      <recent name="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy_alicloud\netty-multiplex-proxy-service" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.xiang.proxy.server" />
      <recent name="com.xiang.proxy.server.config" />
    </key>
  </component>
  <component name="RunManager" selected="JUnit.ProxyServerIntegrationTest.testTransparentModePacketForwarding">
    <configuration name="ProxyClient" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.proxy.client.ProxyClient" />
      <module name="proxy-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.proxy.client.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProxyServerIntegrationTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.integration.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.xiang.proxy.server.integration" />
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.integration.ProxyServerIntegrationTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProxyServerIntegrationTest.testHashBasedQueueDistribution" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.integration.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.xiang.proxy.server.integration" />
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.integration.ProxyServerIntegrationTest" />
      <option name="METHOD_NAME" value="testHashBasedQueueDistribution" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProxyServerIntegrationTest.testQueueProcessingAndThreadConsumption" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.integration.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.xiang.proxy.server.integration" />
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.integration.ProxyServerIntegrationTest" />
      <option name="METHOD_NAME" value="testQueueProcessingAndThreadConsumption" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProxyServerIntegrationTest.testTransparentModePacketForwarding" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="proxy-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.proxy.server.integration.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.xiang.proxy.server.integration" />
      <option name="MAIN_CLASS_NAME" value="com.xiang.proxy.server.integration.ProxyServerIntegrationTest" />
      <option name="METHOD_NAME" value="testTransparentModePacketForwarding" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Bootstrap" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="netty-multiplex-proxy-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xiang.proxy.server.Bootstrap" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.ProxyClient" />
      <item itemvalue="JUnit.ProxyServerIntegrationTest" />
      <item itemvalue="JUnit.ProxyServerIntegrationTest.testHashBasedQueueDistribution" />
      <item itemvalue="JUnit.ProxyServerIntegrationTest.testQueueProcessingAndThreadConsumption" />
      <item itemvalue="JUnit.ProxyServerIntegrationTest.testTransparentModePacketForwarding" />
      <item itemvalue="Spring Boot.Bootstrap" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.ProxyServerIntegrationTest.testTransparentModePacketForwarding" />
        <item itemvalue="JUnit.ProxyServerIntegrationTest.testQueueProcessingAndThreadConsumption" />
        <item itemvalue="JUnit.ProxyServerIntegrationTest.testHashBasedQueueDistribution" />
        <item itemvalue="JUnit.ProxyServerIntegrationTest" />
        <item itemvalue="Application.ProxyClient" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="49980bdc-5d33-4caf-ac98-620ab2f8b564" name="Changes" comment="" />
      <created>1752834374165</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752834374165</updated>
      <workItem from="1752834374958" duration="287000" />
      <workItem from="1755675237535" duration="36526000" />
      <workItem from="1755714995870" duration="42000" />
      <workItem from="1755741117825" duration="53584000" />
      <workItem from="1755796014671" duration="1377000" />
      <workItem from="1755820190688" duration="37040000" />
      <workItem from="1755862187895" duration="25173000" />
      <workItem from="1755911305780" duration="18510000" />
      <workItem from="1755930604928" duration="954000" />
    </task>
    <task id="LOCAL-00001" summary="代理服务器导入，netty多路复用代理">
      <option name="closed" value="true" />
      <created>1752834469506</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752834469507</updated>
    </task>
    <task id="LOCAL-00002" summary="移除target">
      <option name="closed" value="true" />
      <created>1752834614918</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752834614918</updated>
    </task>
    <task id="LOCAL-00003" summary="完成proxy-server与springcloud alibaba整合。">
      <option name="closed" value="true" />
      <created>1755686970386</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755686970386</updated>
    </task>
    <task id="LOCAL-00004" summary="完成proxy-server与springcloud alibaba整合。">
      <option name="closed" value="true" />
      <created>1755687634526</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755687634526</updated>
    </task>
    <task id="LOCAL-00005" summary="proxy-client实现接入nacos，支持前端负载均衡，从nacos查询可用节点。">
      <option name="closed" value="true" />
      <created>1755696336508</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755696336508</updated>
    </task>
    <task id="LOCAL-00006" summary="proxy-client实现接入nacos，支持前端负载均衡，从nacos查询可用节点。">
      <option name="closed" value="true" />
      <created>1755697762424</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755697762424</updated>
    </task>
    <task id="LOCAL-00007" summary="修复docker-compose部署，通过nacos负载均衡。">
      <option name="closed" value="true" />
      <created>1755715018416</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755715018417</updated>
    </task>
    <task id="LOCAL-00008" summary="proxy-server支持inbound与outbound通过队列解耦合，并改善吞吐量。">
      <option name="closed" value="true" />
      <created>1755746566047</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755746566047</updated>
    </task>
    <task id="LOCAL-00009" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755747147187</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755747147187</updated>
    </task>
    <task id="LOCAL-00010" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755747243198</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755747243198</updated>
    </task>
    <task id="LOCAL-00011" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755763425467</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755763425467</updated>
    </task>
    <task id="LOCAL-00012" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755770082337</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1755770082337</updated>
    </task>
    <task id="LOCAL-00013" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755774354114</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755774354114</updated>
    </task>
    <task id="LOCAL-00014" summary="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码">
      <option name="closed" value="true" />
      <created>1755789394891</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755789394891</updated>
    </task>
    <task id="LOCAL-00015" summary="当前proxy-server改动有严重性能问题，大改保存下。">
      <option name="closed" value="true" />
      <created>1755868101911</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1755868101911</updated>
    </task>
    <task id="LOCAL-00016" summary="当前proxy-server改动有严重性能问题，大改保存下。">
      <option name="closed" value="true" />
      <created>1755868151164</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1755868151164</updated>
    </task>
    <option name="localTasksCounter" value="17" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="2bd49786-28c7-4e17-85b5-7a1e42ae161c" value="TOOL_WINDOW" />
        <entry key="0b9717a2-e48a-498f-994e-8fec4d7762fc" value="TOOL_WINDOW" />
        <entry key="28d4ce46-29e5-41e3-b3b5-5361e8d98e5a" value="TOOL_WINDOW" />
        <entry key="a0bdf37f-ded9-4c82-b790-656720b361ec" value="TOOL_WINDOW" />
        <entry key="969bc060-62bb-47d8-9150-fb69eabb7a89" value="TOOL_WINDOW" />
        <entry key="3b158550-6eaf-4210-a517-0b73605ab9c7" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="0b9717a2-e48a-498f-994e-8fec4d7762fc">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="28d4ce46-29e5-41e3-b3b5-5361e8d98e5a">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="2bd49786-28c7-4e17-85b5-7a1e42ae161c">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="3b158550-6eaf-4210-a517-0b73605ab9c7">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="969bc060-62bb-47d8-9150-fb69eabb7a89">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="a0bdf37f-ded9-4c82-b790-656720b361ec">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/ai_gen/netty_proxy/netty_multiplex_proxy/proxy-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="代理服务器导入，netty多路复用代理" />
    <MESSAGE value="移除target" />
    <MESSAGE value="完成proxy-server与springcloud alibaba整合。" />
    <MESSAGE value="proxy-client实现接入nacos，支持前端负载均衡，从nacos查询可用节点。" />
    <MESSAGE value="修复docker-compose部署，通过nacos负载均衡。" />
    <MESSAGE value="proxy-server支持inbound与outbound通过队列解耦合，并改善吞吐量。" />
    <MESSAGE value="netty-multiplex-proxy-service移除掉proxy-server，源码，改用jar引入，那么只需要维护proxy-server。netty-multiplex-proxy-service只保留cloud相关代码" />
    <MESSAGE value="当前proxy-server改动有严重性能问题，大改保存下。" />
    <option name="LAST_COMMIT_MESSAGE" value="当前proxy-server改动有严重性能问题，大改保存下。" />
  </component>
</project>