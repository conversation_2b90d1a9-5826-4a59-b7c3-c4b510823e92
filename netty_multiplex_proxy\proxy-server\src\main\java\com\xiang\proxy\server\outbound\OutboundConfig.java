package com.xiang.proxy.server.outbound;

import java.util.HashMap;
import java.util.Map;

/**
 * Outbound配置
 */
public class OutboundConfig {
    private int connectTimeout = 5000; // 连接超时时间（毫秒）
    private int readTimeout = 30000;   // 读取超时时间（毫秒）
    private int writeTimeout = 30000;  // 写入超时时间（毫秒）
    private int receiveBufferSize = 2097152; // 接收缓冲区大小
    private int sendBufferSize = 2097152;    // 发送缓冲区大小
    private int lowWaterMark = 1572864;       // 低水位标记
    private int highWaterMark = 2097152;     // 高水位标记
    private boolean keepAlive = true;      // 是否保持连接
    private boolean tcpNoDelay = true;     // 是否禁用Nagle算法
    private int maxRetries = 3;            // 最大重试次数
    private long retryDelay = 1000;        // 重试延迟（毫秒）
    private Map<String, Object> properties = new HashMap<>(); // 扩展属性

    public OutboundConfig() {
    }

    public OutboundConfig(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    // Getters and Setters
    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public int getWriteTimeout() {
        return writeTimeout;
    }

    public void setWriteTimeout(int writeTimeout) {
        this.writeTimeout = writeTimeout;
    }

    public int getReceiveBufferSize() {
        return receiveBufferSize;
    }

    public void setReceiveBufferSize(int receiveBufferSize) {
        this.receiveBufferSize = receiveBufferSize;
    }

    public int getSendBufferSize() {
        return sendBufferSize;
    }

    public void setSendBufferSize(int sendBufferSize) {
        this.sendBufferSize = sendBufferSize;
    }

    public int getLowWaterMark() {
        return lowWaterMark;
    }

    public void setLowWaterMark(int lowWaterMark) {
        this.lowWaterMark = lowWaterMark;
    }

    public int getHighWaterMark() {
        return highWaterMark;
    }

    public void setHighWaterMark(int highWaterMark) {
        this.highWaterMark = highWaterMark;
    }

    public boolean isKeepAlive() {
        return keepAlive;
    }

    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
    }

    public boolean isTcpNoDelay() {
        return tcpNoDelay;
    }

    public void setTcpNoDelay(boolean tcpNoDelay) {
        this.tcpNoDelay = tcpNoDelay;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    public long getRetryDelay() {
        return retryDelay;
    }

    public void setRetryDelay(long retryDelay) {
        this.retryDelay = retryDelay;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties != null ? properties : new HashMap<>();
    }

    // 便捷方法
    public void setProperty(String key, Object value) {
        this.properties.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key) {
        return (T) this.properties.get(key);
    }

    public <T> T getProperty(String key, T defaultValue) {
        T value = getProperty(key);
        return value != null ? value : defaultValue;
    }

    public boolean hasProperty(String key) {
        return this.properties.containsKey(key);
    }

    @Override
    public String toString() {
        return String.format("OutboundConfig{connectTimeout=%d, readTimeout=%d, writeTimeout=%d, " +
                "receiveBufferSize=%d, sendBufferSize=%d, keepAlive=%s, tcpNoDelay=%s, maxRetries=%d}",
                connectTimeout, readTimeout, writeTimeout, receiveBufferSize, sendBufferSize,
                keepAlive, tcpNoDelay, maxRetries);
    }

    // 复制方法
    public OutboundConfig copy() {
        OutboundConfig copy = new OutboundConfig();
        copy.connectTimeout = this.connectTimeout;
        copy.readTimeout = this.readTimeout;
        copy.writeTimeout = this.writeTimeout;
        copy.receiveBufferSize = this.receiveBufferSize;
        copy.sendBufferSize = this.sendBufferSize;
        copy.lowWaterMark = this.lowWaterMark;
        copy.highWaterMark = this.highWaterMark;
        copy.keepAlive = this.keepAlive;
        copy.tcpNoDelay = this.tcpNoDelay;
        copy.maxRetries = this.maxRetries;
        copy.retryDelay = this.retryDelay;
        copy.properties = new HashMap<>(this.properties);
        return copy;
    }

    // 预定义配置
    public static OutboundConfig defaultConfig() {
        return new OutboundConfig();
    }

    public static OutboundConfig fastConfig() {
        OutboundConfig config = new OutboundConfig();
        config.setConnectTimeout(3000);
        config.setReadTimeout(15000);
        config.setWriteTimeout(15000);
        config.setMaxRetries(1);
        config.setRetryDelay(500);
        return config;
    }

    public static OutboundConfig reliableConfig() {
        OutboundConfig config = new OutboundConfig();
        config.setConnectTimeout(10000);
        config.setReadTimeout(60000);
        config.setWriteTimeout(60000);
        config.setMaxRetries(5);
        config.setRetryDelay(2000);
        return config;
    }
}