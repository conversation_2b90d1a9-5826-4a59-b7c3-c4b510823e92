package com.xiang.proxy.server.config;

/**
 * ProxyProcessor配置类
 * 用于配置队列和线程相关参数
 */
public class ProxyProcessorConfig {

    // 默认配置 - 优化性能参数
    public static final int DEFAULT_QUEUE_COUNT = Runtime.getRuntime().availableProcessors() * 2;
    public static final int DEFAULT_QUEUE_CAPACITY = 5000; // 减少队列容量，避免过度缓存
    public static final long DEFAULT_SHUTDOWN_TIMEOUT_SECONDS = 30;
    public static final boolean DEFAULT_ENABLE_QUEUE_MONITORING = true;
    public static final long MAX_CONNECTION_AGE = 20 * 60 * 1000; // 20分钟

    private int queueCount = DEFAULT_QUEUE_COUNT;
    private int queueCapacity = DEFAULT_QUEUE_CAPACITY;
    private long shutdownTimeoutSeconds = DEFAULT_SHUTDOWN_TIMEOUT_SECONDS;
    private boolean enableQueueMonitoring = DEFAULT_ENABLE_QUEUE_MONITORING;
    private String workerThreadPrefix = "proxy-worker-";

    private long maxConnectionAge = MAX_CONNECTION_AGE;

    public ProxyProcessorConfig() {
    }

    public ProxyProcessorConfig(int queueCount, int queueCapacity) {
        this.queueCount = queueCount;
        this.queueCapacity = queueCapacity;
    }

    // Getters and Setters
    public int getQueueCount() {
        return queueCount;
    }

    public ProxyProcessorConfig setQueueCount(int queueCount) {
        if (queueCount <= 0) {
            throw new IllegalArgumentException("Queue count must be positive");
        }
        this.queueCount = queueCount;
        return this;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public ProxyProcessorConfig setQueueCapacity(int queueCapacity) {
        if (queueCapacity <= 0) {
            throw new IllegalArgumentException("Queue capacity must be positive");
        }
        this.queueCapacity = queueCapacity;
        return this;
    }

    public long getShutdownTimeoutSeconds() {
        return shutdownTimeoutSeconds;
    }

    public ProxyProcessorConfig setShutdownTimeoutSeconds(long shutdownTimeoutSeconds) {
        if (shutdownTimeoutSeconds < 0) {
            throw new IllegalArgumentException("Shutdown timeout must be non-negative");
        }
        this.shutdownTimeoutSeconds = shutdownTimeoutSeconds;
        return this;
    }

    public boolean isEnableQueueMonitoring() {
        return enableQueueMonitoring;
    }

    public ProxyProcessorConfig setEnableQueueMonitoring(boolean enableQueueMonitoring) {
        this.enableQueueMonitoring = enableQueueMonitoring;
        return this;
    }

    public String getWorkerThreadPrefix() {
        return workerThreadPrefix;
    }

    public ProxyProcessorConfig setWorkerThreadPrefix(String workerThreadPrefix) {
        if (workerThreadPrefix == null || workerThreadPrefix.trim().isEmpty()) {
            throw new IllegalArgumentException("Worker thread prefix cannot be null or empty");
        }
        this.workerThreadPrefix = workerThreadPrefix;
        return this;
    }

    /**
     * 创建默认配置
     */
    public static ProxyProcessorConfig defaultConfig() {
        return new ProxyProcessorConfig();
    }

    @Override
    public String toString() {
        return String.format("ProxyProcessorConfig{queueCount=%d, queueCapacity=%d, " +
                        "shutdownTimeoutSeconds=%d, enableQueueMonitoring=%s, workerThreadPrefix='%s'}",
                queueCount, queueCapacity, shutdownTimeoutSeconds,
                enableQueueMonitoring, workerThreadPrefix);
    }

    public void setMaxConnectionAge(long maxConnectionAge) {
        this.maxConnectionAge = maxConnectionAge;
    }

    public long getMaxConnectionAge() {
        return this.maxConnectionAge;
    }
}