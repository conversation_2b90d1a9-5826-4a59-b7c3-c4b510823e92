package com.xiang.proxy.server.ssl;

import com.xiang.proxy.server.config.ProxyServerV2ConfigManager;
import com.xiang.proxy.server.config.properties.ProxyServerV2Properties;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.SelfSignedCertificate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.KeyStore;

/**
 * SSL上下文管理器
 * 负责创建和管理SSL上下文
 */
public class SslContextManager {
    private static final Logger logger = LoggerFactory.getLogger(SslContextManager.class);

    private static SslContextManager instance;
    private SslContext serverSslContext;
    private final ProxyServerV2ConfigManager configManager;

    private SslContextManager() {
        this.configManager = ProxyServerV2ConfigManager.getInstance();
    }

    public static synchronized SslContextManager getInstance() {
        if (instance == null) {
            instance = new SslContextManager();
        }
        return instance;
    }

    /**
     * 获取服务器SSL上下文
     */
    public SslContext getServerSslContext() {
        if (serverSslContext == null) {
            synchronized (this) {
                if (serverSslContext == null) {
                    serverSslContext = createServerSslContext();
                }
            }
        }
        return serverSslContext;
    }

    /**
     * 创建服务器SSL上下文
     */
    private SslContext createServerSslContext() {
        try {
            ProxyServerV2Properties.SslProperties sslConfig = configManager.getProperties().getSsl();

            if (!sslConfig.isEnable()) {
                logger.info("SSL功能未启用");
                return null;
            }

            SslContextBuilder builder;

            // 检查是否配置了密钥库路径
            if (sslConfig.getKeyStorePath() != null && !sslConfig.getKeyStorePath().trim().isEmpty()) {
                // 使用配置的密钥库
                builder = createSslContextFromKeyStore(sslConfig);
                logger.info("使用配置的密钥库创建SSL上下文: {}", sslConfig.getKeyStorePath());
            } else {
                // 使用自签名证书
                builder = createSelfSignedSslContext();
                logger.warn("未配置密钥库，使用自签名证书创建SSL上下文（仅用于开发测试）");
            }

            // 配置协议版本
            if (sslConfig.getProtocols() != null && !sslConfig.getProtocols().isEmpty()) {
                builder.protocols(sslConfig.getProtocols());
                logger.info("配置SSL协议: {}", sslConfig.getProtocols());
            }

            // 配置密码套件
            if (sslConfig.getCipherSuites() != null && !sslConfig.getCipherSuites().isEmpty()) {
                builder.ciphers(sslConfig.getCipherSuites());
                logger.info("配置密码套件: {}", sslConfig.getCipherSuites());
            }

            // 配置客户端认证
            if (sslConfig.isNeedClientAuth()) {
                builder.clientAuth(io.netty.handler.ssl.ClientAuth.REQUIRE);
                logger.info("启用强制客户端认证");
            } else if (sslConfig.isWantClientAuth()) {
                builder.clientAuth(io.netty.handler.ssl.ClientAuth.OPTIONAL);
                logger.info("启用可选客户端认证");
            } else {
                builder.clientAuth(io.netty.handler.ssl.ClientAuth.NONE);
                logger.info("禁用客户端认证");
            }

            // 配置信任库（用于客户端认证）
            if (sslConfig.isClientAuth() && sslConfig.getTrustStorePath() != null &&
                    !sslConfig.getTrustStorePath().trim().isEmpty()) {
                configureTrustStore(builder, sslConfig);
            }

            SslContext context = builder.build();
            logger.info("SSL上下文创建成功");
            return context;

        } catch (Exception e) {
            logger.error("创建SSL上下文失败", e);
            throw new RuntimeException("Failed to create SSL context", e);
        }
    }

    /**
     * 从密钥库创建SSL上下文
     */
    private SslContextBuilder createSslContextFromKeyStore(ProxyServerV2Properties.SslProperties sslConfig) throws Exception {
        KeyStore keyStore = KeyStore.getInstance(sslConfig.getKeyStoreType());

        // 尝试从类路径或文件系统加载密钥库
        try (InputStream keyStoreStream = loadKeyStoreStream(sslConfig.getKeyStorePath())) {
            keyStore.load(keyStoreStream, sslConfig.getKeyStorePassword().toCharArray());
        }

        KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyManagerFactory.init(keyStore, sslConfig.getKeyStorePassword().toCharArray());

        return SslContextBuilder.forServer(keyManagerFactory);
    }

    /**
     * 创建自签名SSL上下文
     */
    private SslContextBuilder createSelfSignedSslContext() throws Exception {
        SelfSignedCertificate ssc = new SelfSignedCertificate();
        return SslContextBuilder.forServer(ssc.certificate(), ssc.privateKey());
    }

    /**
     * 配置信任库
     */
    private void configureTrustStore(SslContextBuilder builder, ProxyServerV2Properties.SslProperties sslConfig) throws Exception {
        KeyStore trustStore = KeyStore.getInstance(sslConfig.getTrustStoreType());

        try (InputStream trustStoreStream = loadKeyStoreStream(sslConfig.getTrustStorePath())) {
            trustStore.load(trustStoreStream, sslConfig.getTrustStorePassword().toCharArray());
        }

        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init(trustStore);

        builder.trustManager(trustManagerFactory);
        logger.info("配置信任库: {}", sslConfig.getTrustStorePath());
    }

    /**
     * 加载密钥库流
     */
    private InputStream loadKeyStoreStream(String path) throws Exception {
        // 首先尝试从类路径加载
        InputStream stream = getClass().getClassLoader().getResourceAsStream(path);
        if (stream != null) {
            logger.debug("从类路径加载密钥库: {}", path);
            return stream;
        }

        // 然后尝试从文件系统加载
        try {
            stream = new FileInputStream(path);
            logger.debug("从文件系统加载密钥库: {}", path);
            return stream;
        } catch (Exception e) {
            logger.error("无法加载密钥库: {}", path, e);
            throw new RuntimeException("Cannot load keystore: " + path, e);
        }
    }

    /**
     * 检查SSL是否启用
     */
    public boolean isSslEnabled() {
        return configManager.getProperties().getSsl().isEnable();
    }

    /**
     * 获取SSL握手超时时间（秒）
     */
    public int getHandshakeTimeoutSeconds() {
        return configManager.getProperties().getSsl().getHandshakeTimeout().getSeconds();
    }

    /**
     * 获取SSL配置摘要
     */
    public String getSslConfigSummary() {
        ProxyServerV2Properties.SslProperties sslConfig = configManager.getProperties().getSsl();
        if (!sslConfig.isEnable()) {
            return "SSL: disabled";
        }

        return String.format(
                "SSL: enabled, keyStore=%s, protocols=%s, clientAuth=%s",
                sslConfig.getKeyStorePath(),
                sslConfig.getProtocols(),
                sslConfig.isNeedClientAuth() ? "required" :
                        sslConfig.isWantClientAuth() ? "optional" : "none"
        );
    }

    /**
     * 重新加载SSL上下文
     */
    public synchronized void reloadSslContext() {
        logger.info("重新加载SSL上下文");
        serverSslContext = null;
        // 下次调用getServerSslContext()时会重新创建
    }
}
