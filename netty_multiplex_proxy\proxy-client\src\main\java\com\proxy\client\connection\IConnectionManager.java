package com.proxy.client.connection;

import io.netty.buffer.ByteBuf;

/**
 * 连接管理器接口
 * 定义连接管理器的基本操作
 */
public interface IConnectionManager {
    
    /**
     * 启动连接管理器
     */
    void start();
    
    /**
     * 停止连接管理器
     */
    void stop();

    /**
     * 创建简化的relay会话（参考traffic-3.0）
     */
    String createRelaySession(String targetHost, int targetPort, String protocol, SessionHandler handler);

    /**
     * 关闭会话
     */
    void closeSession(int sessionId);
    
    /**
     * 发送数据
     */
    void sendData(int sessionId, byte[] data);
    
    /**
     * 发送数据（ByteBuf版本）
     */
    void sendData(int sessionId, ByteBuf data);
    
    /**
     * 发送UDP数据
     */
    void sendUdpData(int sessionId, byte[] data);
}