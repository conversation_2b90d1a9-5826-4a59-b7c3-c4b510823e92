package com.proxy.client.performance;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 网络性能监控器
 * 用于监控和分析网络性能瓶颈
 */
public class NetworkPerformanceMonitor {
    private static final Logger logger = LoggerFactory.getLogger(NetworkPerformanceMonitor.class);

    private static volatile NetworkPerformanceMonitor instance;

    // 性能统计指标
    private final LongAdder totalBytesReceived = new LongAdder();
    private final LongAdder totalBytesSent = new LongAdder();
    private final LongAdder totalPacketsReceived = new LongAdder();
    private final LongAdder totalPacketsSent = new LongAdder();
    private final AtomicLong lastReportTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong lastBytesReceived = new AtomicLong(0);
    private final AtomicLong lastBytesSent = new AtomicLong(0);

    // 延迟统计
    private final LongAdder totalLatency = new LongAdder();
    private final LongAdder latencyCount = new LongAdder();
    private volatile long maxLatency = 0;
    private volatile long minLatency = Long.MAX_VALUE;

    private NetworkPerformanceMonitor() {
    }

    public static NetworkPerformanceMonitor getInstance() {
        if (instance == null) {
            synchronized (NetworkPerformanceMonitor.class) {
                if (instance == null) {
                    instance = new NetworkPerformanceMonitor();
                }
            }
        }
        return instance;
    }

    /**
     * 记录接收的字节数
     */
    public void recordBytesReceived(long bytes) {
        totalBytesReceived.add(bytes);
        totalPacketsReceived.increment();
    }

    /**
     * 记录发送的字节数
     */
    public void recordBytesSent(long bytes) {
        totalBytesSent.add(bytes);
        totalPacketsSent.increment();
    }

    /**
     * 记录延迟
     */
    public void recordLatency(long latencyMs) {
        totalLatency.add(latencyMs);
        latencyCount.increment();

        // 更新最大最小延迟
        synchronized (this) {
            if (latencyMs > maxLatency) {
                maxLatency = latencyMs;
            }
            if (latencyMs < minLatency) {
                minLatency = latencyMs;
            }
        }
    }

    /**
     * 生成性能报告
     */
    public void generatePerformanceReport() {
        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - lastReportTime.get();

        if (timeDiff < 10000) { // 至少10秒间隔
            return;
        }

        long currentBytesReceived = totalBytesReceived.sum();
        long currentBytesSent = totalBytesSent.sum();
        long currentPacketsReceived = totalPacketsReceived.sum();
        long currentPacketsSent = totalPacketsSent.sum();

        // 计算速率
        long receivedDiff = currentBytesReceived - lastBytesReceived.get();
        long sentDiff = currentBytesSent - lastBytesSent.get();

        double receiveRateMBps = (receivedDiff * 1000.0) / (timeDiff * 1024 * 1024);
        double sendRateMBps = (sentDiff * 1000.0) / (timeDiff * 1024 * 1024);

        // 计算平均延迟
        long latencySum = totalLatency.sum();
        long latencyCountValue = latencyCount.sum();
        double avgLatency = latencyCountValue > 0 ? (double) latencySum / latencyCountValue : 0;

        logger.info("=== 网络性能报告 ===");
        logger.info("接收速率: {} MB/s ({} 字节, {} 包)", String.format("%.2f", receiveRateMBps), receivedDiff, currentPacketsReceived);
        logger.info("发送速率: {} MB/s ({} 字节, {} 包)", String.format("%.2f", sendRateMBps), sentDiff, currentPacketsSent);
        logger.info("延迟统计: 平均 {}ms, 最小 {}ms, 最大 {}ms", String.format("%.2f", avgLatency), minLatency, maxLatency);

        // 性能建议
        if (receiveRateMBps < 1.0 || sendRateMBps < 1.0) {
            logger.warn("检测到低吞吐量，建议检查：");
            logger.warn("1. 网络缓冲区大小是否足够");
            logger.warn("2. 是否存在频繁的小包传输");
            logger.warn("3. 数据包编码/解码是否存在性能瓶颈");
        }

        if (avgLatency > 100) {
            logger.warn("检测到高延迟，建议检查：");
            logger.warn("1. 网络连接质量");
            logger.warn("2. 数据处理逻辑是否存在阻塞");
            logger.warn("3. 线程池配置是否合理");
        }

        // 更新上次报告时间和数据
        lastReportTime.set(currentTime);
        lastBytesReceived.set(currentBytesReceived);
        lastBytesSent.set(currentBytesSent);
    }

    /**
     * 重置统计数据
     */
    public void reset() {
        totalBytesReceived.reset();
        totalBytesSent.reset();
        totalPacketsReceived.reset();
        totalPacketsSent.reset();
        totalLatency.reset();
        latencyCount.reset();
        maxLatency = 0;
        minLatency = Long.MAX_VALUE;
        lastReportTime.set(System.currentTimeMillis());
        lastBytesReceived.set(0);
        lastBytesSent.set(0);
    }

    /**
     * 获取当前吞吐量统计
     */
    public ThroughputStats getCurrentStats() {
        return new ThroughputStats(
                totalBytesReceived.sum(),
                totalBytesSent.sum(),
                totalPacketsReceived.sum(),
                totalPacketsSent.sum()
        );
    }

    /**
     * 吞吐量统计数据类
     */
    public static class ThroughputStats {
        public final long bytesReceived;
        public final long bytesSent;
        public final long packetsReceived;
        public final long packetsSent;

        public ThroughputStats(long bytesReceived, long bytesSent, long packetsReceived, long packetsSent) {
            this.bytesReceived = bytesReceived;
            this.bytesSent = bytesSent;
            this.packetsReceived = packetsReceived;
            this.packetsSent = packetsSent;
        }

        @Override
        public String toString() {
            return String.format("ThroughputStats{received=%d bytes (%d packets), sent=%d bytes (%d packets)}",
                    bytesReceived, packetsReceived, bytesSent, packetsSent);
        }
    }
}
