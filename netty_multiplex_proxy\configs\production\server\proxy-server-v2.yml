# 代理服务器V2配置文件 - 组件化架构

# 全局配置
global:
  name: "ProxyServerV2"
  version: "2.0.0"
  description: "组件化代理服务器"

# 路由配置
routing:
  # 默认路由规则
  default_outbound: "tcpDirect"

  # 自定义路由规则
  #  rules:
  #    - id: "internal-direct"
  #      name: "内网直连"
  #      priority: 10
  #      outbound: "tcpDirect"
  #      matchers:
  #        - type: "host"
  #          operator: "regex"
  #          value: "^(192\\.168\\.|10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
  #
  #    - id: "local-domain"
  #      name: "本地域名"
  #      priority: 20
  #      outbound: "tcpDirect"
  #      matchers:
  #        - type: "host"
  #          operator: "ends_with"
  #          value: ".local"
  #
  #    - id: "http-ports"
  #      name: "HTTP端口"
  #      priority: 30
  #      outbound: "tcpDirect"
  #      matchers:
  #        - type: "port"
  #          operator: "in"
  #          value: "80,443,8080,8443"
  rules:
    #一条rule满足就路由
    - id: "tcp-direct"
      name: "tcp直连"
      priority: 10
      outbound: "tcpDirect"
      #matcher需要所有都满足(and)
      matchers:
        - type: "protocol"
          operator: "regex"
          value: "^tcp$"
          caseSensitive: false
    - id: "udp-direct"
      name: "udp直连"
      priority: 10
      outbound: "udpDirect"
      matchers:
        - type: "protocol"
          operator: "regex"
          value: "^udp$"
          caseSensitive: false

# Inbound服务器配置
inbounds:
  - id: "main-multiplex"
    type: "multiplex"
    name: "主多路复用服务器"
    enabled: true
    config:
      port: 8888
      bind_address: "0.0.0.0"
      boss_threads: 2
      worker_threads: 0  # 0表示自动计算
      backlog: 2048
      max_connections: 50000
      receive_buffer_size: 128000
      send_buffer_size: 128000
      low_water_mask: 1572864
      high_water_mark: 2097152
      keep_alive: true
      tcp_no_delay: true
      reuse_address: true
      so_timeout: 30000
      connect_timeout: 5000
      enable_ssl: true
#  - id: "high-perf-multiplex"
#    type: "multiplex"
#    name: "高性能多路复用服务器"
#    enabled: true
#    config:
#      port: 8081
#      bind_address: "0.0.0.0"
#      boss_threads: 4
#      worker_threads: 32
#      backlog: 8192
#      max_connections: 100000
#      receive_buffer_size: 262144
#      send_buffer_size: 262144
#      keep_alive: true
#      tcp_no_delay: true
#      reuse_address: true
#      so_timeout: 30000
#      connect_timeout: 5000
#
#  - id: "ssl-multiplex"
#    type: "multiplex"
#    name: "SSL多路复用服务器"
#    enabled: false
#    config:
#      port: 8443
#      bind_address: "0.0.0.0"
#      boss_threads: 2
#      worker_threads: 16
#      backlog: 2048
#      max_connections: 20000
#      enable_ssl: true
#      ssl_key_store: "configs/server.p12"
#      ssl_key_store_password: "password"
#      ssl_trust_store: "configs/truststore.p12"
#      ssl_trust_store_password: "password"

# Outbound配置
outbounds:
  - id: "tcpDirect"
    type: "tcp_direct"
    name: "tcp直连"
    enabled: true
    config:
      connect_timeout: 5000
      read_timeout: 30000
      write_timeout: 30000
      max_retries: 3
      retry_delay: 1000
      keep_alive: true
      tcp_no_delay: true
      receive_buffer_size: 128000
      send_buffer_size: 128000
      low_water_mark: 1572864
      high_water_mark: 2097152
  - id: "udpDirect"
    type: "udp_direct"
    name: "udp直连"
    enabled: true
    config:
      receive_buffer_size: 65536
      send_buffer_size: 65536
      low_water_mark: 8192
      high_water_mark: 32768
#  - id: "upstream-proxy"
#    type: "proxy_chain"
#    name: "上游代理"
#    enabled: false
#    config:
#      proxy_host: "upstream.proxy.com"
#      proxy_port: 8080
#      proxy_type: "http"
#      connect_timeout: 10000
#      read_timeout: 60000
#      write_timeout: 60000
#      max_retries: 2
#      retry_delay: 2000
#
#  - id: "load-balancer"
#    type: "load_balancer"
#    name: "负载均衡"
#    enabled: false
#    config:
#      strategy: "round_robin"  # round_robin, random, least_connections
#      backends:
#        - host: "backend1.example.com"
#          port: 8080
#          weight: 1
#        - host: "backend2.example.com"
#          port: 8080
#          weight: 2
#      health_check:
#        enabled: true
#        interval: 30
#        timeout: 5
#        path: "/health"


# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 30

# 连接管理配置（已移除ConnectionPool，使用异步连接管理）
connection:
  # 连接超时配置
  timeout:
    connect: 5000    # 连接建立超时（毫秒）
    read: 30000      # 读取超时（毫秒）
    write: 30000     # 写入超时（毫秒）
  # 连接清理配置
  cleanup:
    interval: 60     # 清理间隔（秒）
    idle_threshold: 300  # 空闲连接清理阈值（秒）

# 性能监控配置
metrics:
  enable: true
  report:
    interval:
      seconds: 300  # 5分钟报告一次

# 黑名单配置
blacklist:
  enable: true
  failure:
    threshold: 3  # 失败3次后加入黑名单
  cache:
    timeout:
      seconds: 180  # 黑名单缓存3分钟

# 性能配置
performance:
  # Boss线程数 (0表示自动计算)
  boss-threads: 0
  # 工作线程数 (0表示自动计算，基于CPU核心数和I/O比例)
  worker-threads: 0
  # I/O操作与CPU操作的比例 (1-100)，开发环境可以设置较低值
  io-ratio: 60
  # 是否启用智能线程优化
  enable-thread-optimization: true
  # 最大工作线程数 (0表示无限制，开发环境建议限制)
  max-worker-threads: 32
  # 最小工作线程数 (开发环境可以设置较小值)
  min-worker-threads: 2

# SSL/TLS配置
#执行scripts/gen-ssl-certs.bat 自动生成证书
ssl:
  enable: true  # 是否启用SSL/TLS
  key-store-path: "server.p12"  # 密钥库路径（相对于classpath或绝对路径）
  key-store-password: "xiang1"  # 密钥库密码
  key-store-type: "PKCS12"  # 密钥库类型
  trust-store-path: "truststore.p12"  # 信任库路径（用于客户端认证）
  trust-store-password: "xiang1"  # 信任库密码
  trust-store-type: "PKCS12"  # 信任库类型
  protocols: # 支持的SSL/TLS协议版本
    - "TLSv1.2"
    - "TLSv1.3"
  # cipher-suites: # 支持的加密套件（可选）
  #   - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
  #   - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
  client-auth: false  # 是否要求客户端认证
  need-client-auth: false  # 是否必须客户端认证
  want-client-auth: false  # 是否可选客户端认证
  handshake-timeout:
    seconds: 30  # SSL握手超时时间

# 地理位置过滤配置
geo-location-filter:
  enable: false  # 是否启用地理位置过滤
  block-overseas-suspicious: false  # 是否阻止海外可疑网站
  enable-domain-filter: true  # 是否启用域名过滤
  enable-keyword-filter: true  # 是否启用关键字过滤
  enable-whitelist: true  # 是否启用白名单
  dns-cache-timeout:
    minutes: 5  # DNS缓存超时时间
  ip-cache-timeout:
    minutes: 60  # IP缓存超时时间
  max-cache-size: 10000  # 最大缓存条目数
  auto-update-ip-ranges: true  # 是否自动更新IP段数据
  update-interval:
    hours: 24  # 更新间隔时间
  online-data-sources: # 在线数据源
    malicious-domains:
      - "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts"
      - "https://someonewhocares.org/hosts/zero/hosts"
      - "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"
    malicious-keywords:
      - "https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt"
    china-ip-ranges:
      - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
      - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"