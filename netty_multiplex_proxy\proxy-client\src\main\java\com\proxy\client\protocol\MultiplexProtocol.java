package com.proxy.client.protocol;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.buffer.Unpooled;

/**
 * 多路复用协议定义
 * 定义proxy-client与proxy-server之间的通信协议
 */
public class MultiplexProtocol {

    // 协议魔数
    public static final short MAGIC = (short) 0x5050; // PP - Proxy Protocol

    // 数据包类型
    public static final byte TYPE_RELAY_REQUEST = 0x0C;     // 简化的relay连接请求（参考traffic-3.0）
    public static final byte TYPE_RELAY_RESPONSE = 0x0D;    // 简化的relay连接响应
    public static final byte TYPE_AUTH_REQUEST = 0x08;      // 认证请求
    public static final byte TYPE_AUTH_RESPONSE = 0x09;     // 认证响应
    public static final byte TYPE_DATA = 0x03;              // 数据传输
    public static final byte TYPE_CLOSE = 0x04;             // 关闭连接
    public static final byte TYPE_HEARTBEAT = 0x05;         // 心跳

    // 响应状态码
    public static final byte STATUS_SUCCESS = 0x00;        // 成功
    public static final byte STATUS_FAILED = 0x01;         // 失败
    public static final byte STATUS_HOST_UNREACHABLE = 0x02; // 主机不可达
    public static final byte STATUS_AUTH_REQUIRED = 0x03;   // 需要认证
    public static final byte STATUS_AUTH_FAILED = 0x04;     // 认证失败

    // 数据类型
    public static final byte DATA_TYPE_TCP = 0x00;          // TCP数据
    public static final byte DATA_TYPE_UDP = 0x01;          // UDP数据

    // 协议头长度
    public static final int HEADER_LENGTH = 13; // 2 + 1 + 1 + 1 + 4 + 4

    /**
     * 多路复用数据包
     */
    public static class Packet {
        private final byte type;
        private final byte dataType; // TCP/UDP数据类型
        private final int sessionId;
        private final byte[] data;
        private final ByteBuf dataBuf; // 优化：支持直接使用ByteBuf

        public Packet(byte type, int sessionId, byte[] data) {
            this(type, DATA_TYPE_TCP, sessionId, data); // 默认为TCP
        }

        public Packet(byte type, byte dataType, int sessionId, byte[] data) {
            this.type = type;
            this.dataType = dataType;
            this.sessionId = sessionId;
            this.data = data != null ? data : new byte[0];
            this.dataBuf = null;
        }

        // 优化：支持直接使用ByteBuf的构造函数
        private Packet(byte type, byte dataType, int sessionId, ByteBuf dataBuf, boolean isDataBuf) {
            this.type = type;
            this.dataType = dataType;
            this.sessionId = sessionId;
            this.dataBuf = dataBuf;
            // 延迟转换为byte[]，只在需要时进行
            this.data = null;
        }

        // 工厂方法创建ByteBuf版本的Packet
        public static Packet createWithByteBuf(byte type, int sessionId, ByteBuf dataBuf) {
            return new Packet(type, DATA_TYPE_TCP, sessionId, dataBuf, true);
        }

        public static Packet createWithByteBuf(byte type, byte dataType, int sessionId, ByteBuf dataBuf) {
            return new Packet(type, dataType, sessionId, dataBuf, true);
        }

        public byte getType() {
            return type;
        }

        public byte getDataType() {
            return dataType;
        }

        public int getSessionId() {
            return sessionId;
        }

        public boolean isTcp() {
            return dataType == DATA_TYPE_TCP;
        }

        public boolean isUdp() {
            return dataType == DATA_TYPE_UDP;
        }

        public byte[] getData() {
            if (data != null) {
                return data;
            }
            // 从ByteBuf转换
            if (dataBuf != null && dataBuf.isReadable()) {
                byte[] bytes = new byte[dataBuf.readableBytes()];
                dataBuf.getBytes(dataBuf.readerIndex(), bytes);
                return bytes;
            }
            return new byte[0];
        }

        // 优化：直接获取ByteBuf，避免数据拷贝
        public ByteBuf getDataBuf() {
            return dataBuf;
        }

        public int getDataLength() {
            if (data != null) {
                return data.length;
            }
            if (dataBuf != null) {
                return dataBuf.readableBytes();
            }
            return 0;
        }

        /**
         * 将数据包编码为ByteBuf
         */
        public ByteBuf encode() {
            int dataLength = getDataLength();

            // 零拷贝优化：参考traffic-3.0，对于数据包使用CompositeByteBuf
            if (dataBuf != null && type == TYPE_DATA) {
                return encodeZeroCopy(dataLength);
            }

            // 传统编码方式（用于非数据包）
            return encodeTraditional(dataLength);
        }

        /**
         * 零拷贝编码 - 参考traffic-3.0的实现
         */
        private ByteBuf encodeZeroCopy(int dataLength) {
            // 创建协议头
            ByteBuf header = PooledByteBufAllocator.DEFAULT.buffer(HEADER_LENGTH);
            header.writeShort(MAGIC);           // Magic (2 bytes)
            header.writeByte(type);             // Type (1 byte)
            header.writeByte(dataType);         // DataType (1 byte) - TCP/UDP
            header.writeByte(0);                // Reserved (1 byte)
            header.writeInt(sessionId);         // SessionID (4 bytes)
            header.writeInt(dataLength);       // Length (4 bytes)

            // 使用CompositeByteBuf组合协议头和数据，避免数据拷贝
            if (dataLength > 0 && dataBuf != null) {
                return Unpooled.wrappedBuffer(header, dataBuf.retain());
            } else {
                return header;
            }
        }

        /**
         * 传统编码方式
         */
        private ByteBuf encodeTraditional(int dataLength) {
            ByteBuf buffer = PooledByteBufAllocator.DEFAULT.buffer(HEADER_LENGTH + dataLength);

            // 写入协议头
            buffer.writeShort(MAGIC);           // Magic (2 bytes)
            buffer.writeByte(type);             // Type (1 byte)
            buffer.writeByte(dataType);         // DataType (1 byte) - TCP/UDP
            buffer.writeByte(0);                // Reserved (1 byte)
            buffer.writeInt(sessionId);         // SessionID (4 bytes)
            buffer.writeInt(dataLength);       // Length (4 bytes)

            // 写入数据
            if (dataLength > 0) {
                if (data != null) {
                    buffer.writeBytes(data);
                } else if (dataBuf != null) {
                    buffer.writeBytes(dataBuf);
                }
            }

            return buffer;
        }

        /**
         * 从ByteBuf解码数据包
         */
        public static Packet decode(ByteBuf buffer) {
            if (buffer.readableBytes() < HEADER_LENGTH) {
                return null; // 数据不足
            }

            int readerIndex = buffer.readerIndex();

            // 检查魔数
            short magic = buffer.readShort();
            if (magic != MAGIC) {
                throw new IllegalArgumentException("Invalid magic number: " + magic);
            }

            // 读取协议头
            byte type = buffer.readByte();
            byte dataType = buffer.readByte();  // DataType (TCP/UDP)
            buffer.readByte(); // 跳过保留字段
            int sessionId = buffer.readInt();
            int dataLength = buffer.readInt();

            // 检查数据长度
            if (buffer.readableBytes() < dataLength) {
                // 重置读取位置，等待更多数据
                buffer.readerIndex(readerIndex);
                return null;
            }

            // 优化：对于数据包，直接使用ByteBuf slice避免数据拷贝
            if (dataLength > 0 && type == TYPE_DATA) {
                ByteBuf dataBuf = buffer.readSlice(dataLength).retain();
                return createWithByteBuf(type, dataType, sessionId, dataBuf);
            } else {
                // 对于控制包，仍使用byte[]
                byte[] data = new byte[dataLength];
                if (dataLength > 0) {
                    buffer.readBytes(data);
                }
                return new Packet(type, dataType, sessionId, data);
            }
        }

        @Override
        public String toString() {
            return String.format("Packet{type=%d, dataType=%s, sessionId=%d, dataLength=%d}",
                    type, dataType == DATA_TYPE_TCP ? "TCP" : "UDP", sessionId, getDataLength());
        }
    }


    /**
     * 创建简化的relay连接请求数据包（参考traffic-3.0）
     * 包含requestId用于标识请求，不需要复杂的sessionId交互
     */
    public static Packet createRelayRequest(String requestId, String host, int port, String protocol) {
        // 格式: requestId|protocol|host:port
        String connectData;

        // 检查是否为IPv6地址
        if (host.contains(":") && !host.startsWith("[")) {
            // IPv6地址需要用方括号包围
            connectData = requestId + "|" + protocol + "|[" + host + "]:" + port;
        } else {
            // IPv4地址或域名，或者已经格式化的IPv6地址
            connectData = requestId + "|" + protocol + "|" + host + ":" + port;
        }

        byte dataType = "UDP".equals(protocol) ? DATA_TYPE_UDP : DATA_TYPE_TCP;
        return new Packet(TYPE_RELAY_REQUEST, dataType, 0, connectData.getBytes());
    }

    /**
     * 创建简化的relay连接响应数据包
     */
    public static Packet createRelayResponse(String requestId, byte status) {
        String responseData = requestId + "|" + status;
        return new Packet(TYPE_RELAY_RESPONSE, 0, responseData.getBytes());
    }


    /**
     * 创建TCP数据传输数据包
     */
    public static Packet createTcpDataPacket(int sessionId, byte[] data) {
        return new Packet(TYPE_DATA, DATA_TYPE_TCP, sessionId, data);
    }

    /**
     * 创建TCP数据传输数据包 (ByteBuf版本) - 零拷贝优化
     */
    public static Packet createTcpDataPacket(int sessionId, ByteBuf data) {
        return Packet.createWithByteBuf(TYPE_DATA, DATA_TYPE_TCP, sessionId, data);
    }

    /**
     * 创建零拷贝数据包 - 参考traffic-3.0的实现
     * 直接传递ByteBuf引用，避免数据拷贝
     */
    public static Packet createZeroCopyDataPacket(int sessionId, ByteBuf data, boolean isTcp) {
        byte dataType = isTcp ? DATA_TYPE_TCP : DATA_TYPE_UDP;
        return Packet.createWithByteBuf(TYPE_DATA, dataType, sessionId, data.retain());
    }

    /**
     * 创建UDP数据传输数据包
     */
    public static Packet createUdpDataPacket(int sessionId, byte[] data) {
        return new Packet(TYPE_DATA, DATA_TYPE_UDP, sessionId, data);
    }

    /**
     * 创建指定类型的数据传输数据包
     */
    public static Packet createTcpDataPacket(int sessionId, byte dataType, byte[] data) {
        return new Packet(TYPE_DATA, dataType, sessionId, data);
    }

    /**
     * 创建关闭连接数据包
     */
    public static Packet createClosePacket(int sessionId) {
        return new Packet(TYPE_CLOSE, sessionId, null);
    }

    /**
     * 创建心跳数据包
     */
    public static Packet createHeartbeatPacket() {
        return new Packet(TYPE_HEARTBEAT, 0, null);
    }

    /**
     * 创建认证请求数据包
     */
    public static Packet createAuthRequestPacket(String username, String password) {
        String authData = username + ":" + password;
        return new Packet(TYPE_AUTH_REQUEST, 0, authData.getBytes());
    }

    /**
     * 创建认证响应数据包
     */
    public static Packet createAuthResponsePacket(byte status) {
        return new Packet(TYPE_AUTH_RESPONSE, 0, new byte[]{status});
    }


    /**
     * 解析relay连接请求数据
     * 格式: requestId|protocol|host:port
     * 返回: [requestId, protocol, host, port]
     */
    public static String[] parseRelayRequest(Packet packet) {
        if (packet.getType() != TYPE_RELAY_REQUEST) {
            throw new IllegalArgumentException("Not a relay request packet, type: " + packet.getType());
        }

        String connectData = new String(packet.getData());
        String[] parts = connectData.split("\\|", 3); // 分割为3部分: requestId, protocol, host:port
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid relay request format: " + connectData);
        }

        String requestId = parts[0];
        String protocol = parts[1];
        String hostPort = parts[2];

        // 解析host:port部分
        String host, portStr;
        if (hostPort.startsWith("[")) {
            // IPv6格式: [::1]:9229 或 [2001:db8::1]:8080
            int closeBracket = hostPort.indexOf(']');
            if (closeBracket == -1 || closeBracket + 1 >= hostPort.length() || hostPort.charAt(closeBracket + 1) != ':') {
                throw new IllegalArgumentException("Invalid IPv6 relay request format: " + hostPort);
            }

            host = hostPort.substring(1, closeBracket); // 去掉方括号
            portStr = hostPort.substring(closeBracket + 2); // 跳过 ]:
        } else {
            // IPv4格式或域名格式: host:port
            int lastColon = hostPort.lastIndexOf(':');
            if (lastColon == -1 || lastColon == 0 || lastColon == hostPort.length() - 1) {
                throw new IllegalArgumentException("Invalid relay request format: " + hostPort);
            }

            host = hostPort.substring(0, lastColon);
            portStr = hostPort.substring(lastColon + 1);
        }

        return new String[]{requestId, protocol, host, portStr};
    }

    /**
     * 解析relay连接响应数据
     * 格式: requestId|status
     * 返回: [requestId, status]
     */
    public static String[] parseRelayResponse(Packet packet) {
        if (packet.getType() != TYPE_RELAY_RESPONSE) {
            throw new IllegalArgumentException("Not a relay response packet, type: " + packet.getType());
        }

        String responseData = new String(packet.getData());
        String[] parts = responseData.split("\\|", 2); // 分割为2部分: requestId, status
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid relay response format: " + responseData);
        }

        return parts; // [requestId, status]
    }

    /**
     * 解析认证请求数据
     */
    public static String[] parseAuthRequest(Packet packet) {
        if (packet.getType() != TYPE_AUTH_REQUEST) {
            throw new IllegalArgumentException("Not an auth request packet");
        }

        String authData = new String(packet.getData());
        String[] parts = authData.split(":", 2); // 限制分割为2部分，防止密码中包含冒号
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid auth request format: " + authData);
        }

        return parts; // [username, password]
    }

    /**
     * 解析认证响应状态
     */
    public static byte parseAuthResponse(Packet packet) {
        if (packet.getType() != TYPE_AUTH_RESPONSE) {
            throw new IllegalArgumentException("Not an auth response packet");
        }

        if (packet.getDataLength() < 1) {
            throw new IllegalArgumentException("Invalid auth response packet");
        }

        return packet.getData()[0];
    }
}
