package com.xiang.proxy.server.integration;

import com.xiang.proxy.server.ProxyServerV2;
import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.core.ProxyResponse;
import com.xiang.proxy.server.util.ConnectionKeyUtils;
import com.xiang.proxy.server.protocol.MultiplexProtocol;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.inbound.impl.multiplex.MultiplexInboundServer;
import com.xiang.proxy.server.config.ProxyServerV2ConfigManager;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Proxy Server 集成测试
 * 测试hash分配包到队列、线程消费队列建立连接、透明模式发包和relay建立功能
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ProxyServerIntegrationTest {
    private static final Logger logger = LoggerFactory.getLogger(ProxyServerIntegrationTest.class);
    
    private ProxyServerV2 proxyServer;
    private ProxyProcessor proxyProcessor;
    private static final int TEST_PORT = 18888;
    private static final String TEST_HOST = "localhost";
    
    @BeforeEach
    void setUp() throws Exception {
        logger.info("设置测试环境...");
        
        // 设置测试配置
        System.setProperty("proxy.server.port", String.valueOf(TEST_PORT));
        System.setProperty("proxy.server.host", TEST_HOST);
        System.setProperty("proxy.server.queue.count", "4");
        System.setProperty("proxy.server.queue.capacity", "1000");
        
        // 创建并启动proxy server
        proxyServer = new ProxyServerV2();
        CompletableFuture<Void> startFuture = proxyServer.start();
        
        // 等待启动完成，最多等待30秒
        try {
            startFuture.get(30, TimeUnit.SECONDS);
            logger.info("ProxyServer启动成功");
        } catch (TimeoutException e) {
            logger.error("ProxyServer启动超时");
            throw new RuntimeException("ProxyServer启动超时", e);
        }
        
        // 获取ProxyProcessor实例用于测试
        // 注意：这里需要通过反射或者提供getter方法来获取
        // proxyProcessor = proxyServer.getProxyProcessor();
        
        Thread.sleep(2000); // 等待服务完全启动
    }
    
    @AfterEach
    void tearDown() throws Exception {
        logger.info("清理测试环境...");
        if (proxyServer != null) {
            proxyServer.stop();
        }
        Thread.sleep(1000); // 等待完全关闭
    }
    
    /**
     * 测试1: Hash分配包到队列功能
     * 验证相同的连接键值总是分配到同一个队列
     */
    @Test
    @Order(1)
    void testHashBasedQueueDistribution() {
        logger.info("=== 测试Hash分配包到队列功能 ===");
        
        // 测试数据
        String[] hosts = {"example.com", "google.com", "github.com", "stackoverflow.com"};
        int[] ports = {80, 443, 8080, 9090};
        String[] protocols = {"HTTP", "HTTPS", "TCP", "UDP"};
        String clientId = "test-client-001";
        
        Map<String, Integer> keyToQueueMap = new HashMap<>();
        int queueCount = 4;
        
        // 测试多次分配，验证一致性
        for (int round = 0; round < 3; round++) {
            logger.info("第{}轮分配测试", round + 1);
            
            for (String host : hosts) {
                for (int port : ports) {
                    for (String protocol : protocols) {
                        // 生成连接键值
                        String connectionKey = ConnectionKeyUtils.generateQueueKey(host, port, protocol, clientId);
                        
                        // 计算队列索引
                        int queueIndex = ConnectionKeyUtils.calculateQueueIndex(connectionKey, queueCount);
                        
                        // 验证队列索引范围
                        assertTrue(queueIndex >= 0 && queueIndex < queueCount, 
                            "队列索引超出范围: " + queueIndex);
                        
                        // 验证一致性
                        if (keyToQueueMap.containsKey(connectionKey)) {
                            assertEquals(keyToQueueMap.get(connectionKey), queueIndex,
                                "相同连接键值应该分配到相同队列: " + connectionKey);
                        } else {
                            keyToQueueMap.put(connectionKey, queueIndex);
                        }
                        
                        logger.debug("连接键值: {} -> 队列: {}", connectionKey, queueIndex);
                    }
                }
            }
        }
        
        // 验证分布均匀性
        int[] queueCounts = new int[queueCount];
        for (int queueIndex : keyToQueueMap.values()) {
            queueCounts[queueIndex]++;
        }
        
        logger.info("队列分布统计:");
        for (int i = 0; i < queueCount; i++) {
            logger.info("队列{}: {}个连接键值", i, queueCounts[i]);
        }
        
        // 验证每个队列都有分配（基本均匀性检查）
        for (int i = 0; i < queueCount; i++) {
            assertTrue(queueCounts[i] > 0, "队列" + i + "没有分配到任何连接键值");
        }
        
        logger.info("Hash分配包到队列功能测试通过 ✓");
    }
    
    /**
     * 测试2: 队列处理和线程消费功能
     * 验证每个队列都有对应的工作线程在处理请求
     */
    @Test
    @Order(2)
    void testQueueProcessingAndThreadConsumption() throws Exception {
        logger.info("=== 测试队列处理和线程消费功能 ===");
        
        // 创建测试请求
        List<ProxyRequest> testRequests = createTestRequests(20);
        List<CompletableFuture<ProxyResponse>> futures = new ArrayList<>();
        
        // 记录每个队列处理的请求数
        Map<Integer, AtomicInteger> queueProcessCounts = new HashMap<>();
        for (int i = 0; i < 4; i++) {
            queueProcessCounts.put(i, new AtomicInteger(0));
        }
        
        // 发送测试请求
        for (ProxyRequest request : testRequests) {
            // 计算预期的队列索引
            int expectedQueueIndex = ConnectionKeyUtils.calculateQueueIndex(request, 4);
            queueProcessCounts.get(expectedQueueIndex).incrementAndGet();
            
            // 这里需要实际的ProxyProcessor实例来处理请求
            // CompletableFuture<ProxyResponse> future = proxyProcessor.processRequest(request);
            // futures.add(future);
            
            logger.debug("请求 {} 预期分配到队列 {}", request.getRequestId(), expectedQueueIndex);
        }
        
        // 等待所有请求处理完成
        // CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(30, TimeUnit.SECONDS);
        
        // 验证处理结果
        logger.info("队列处理统计:");
        for (Map.Entry<Integer, AtomicInteger> entry : queueProcessCounts.entrySet()) {
            int queueIndex = entry.getKey();
            int processCount = entry.getValue().get();
            logger.info("队列{}: 处理{}个请求", queueIndex, processCount);
            
            if (processCount > 0) {
                // 验证该队列确实处理了请求
                assertTrue(processCount > 0, "队列" + queueIndex + "应该处理了请求");
            }
        }
        
        logger.info("队列处理和线程消费功能测试通过 ✓");
    }
    
    /**
     * 创建测试请求
     */
    private List<ProxyRequest> createTestRequests(int count) {
        List<ProxyRequest> requests = new ArrayList<>();
        String[] hosts = {"httpbin.org", "example.com", "google.com"};
        int[] ports = {80, 443, 8080};
        
        for (int i = 0; i < count; i++) {
            String host = hosts[i % hosts.length];
            int port = ports[i % ports.length];
            
            ProxyRequest request = ProxyRequest.builder()
                .requestId("test-request-" + i)
                .target(host, port)
                .protocol("HTTP")
                .clientId("test-client-001")
                .clientChannel(new EmbeddedChannel())
                .data(Unpooled.copiedBuffer("GET / HTTP/1.1\r\nHost: " + host + "\r\n\r\n",
                    java.nio.charset.StandardCharsets.UTF_8))
                .build();
            
            requests.add(request);
        }
        
        return requests;
    }
    
    /**
     * 测试3: 透明模式发包功能
     * 验证数据包能够正确通过透明代理转发
     */
    @Test
    @Order(3)
    void testTransparentModePacketForwarding() throws Exception {
        logger.info("=== 测试透明模式发包功能 ===");
        
        // 创建透明模式测试数据
        String targetHost = "httpbin.org";
        int targetPort = 80;
        String testData = "GET /ip HTTP/1.1\r\nHost: httpbin.org\r\nConnection: close\r\n\r\n";
        
        // 创建透明代理请求
        ProxyRequest transparentRequest = ProxyRequest.builder()
            .requestId("transparent-test-001")
            .target(targetHost, targetPort)
            .protocol("HTTP")
            .clientId("transparent-client")
            .clientChannel(new EmbeddedChannel())
            .data(Unpooled.copiedBuffer(testData, java.nio.charset.StandardCharsets.UTF_8))
            .attribute("transparent", true) // 标记为透明模式
            .build();

        logger.info("创建透明代理请求: target={}:{}, transparent={}",
            targetHost, targetPort, transparentRequest.getAttribute("transparent", false));
        
        // 这里需要实际的ProxyProcessor来处理透明请求
         CompletableFuture<ProxyResponse> future = proxyProcessor.processRequest(transparentRequest);
         ProxyResponse response = future.get(30, TimeUnit.SECONDS);
        
        // 验证透明模式处理
         assertTrue(response.isSuccess(), "透明模式请求应该成功处理");
         assertNotNull(response.getConnection(), "应该建立了连接");
        
        logger.info("透明模式发包功能测试通过 ✓");
    }

    /**
     * 测试4: Relay连接建立功能
     * 验证多路复用协议的relay连接能够正确建立
     */
    @Test
    @Order(4)
    void testRelayConnectionEstablishment() throws Exception {
        logger.info("=== 测试Relay连接建立功能 ===");

        // 创建relay连接请求
        String requestId = "relay-test-001";
        String targetHost = "httpbin.org";
        int targetPort = 80;
        String protocol = "HTTP";

        // 创建多路复用relay请求包
        MultiplexProtocol.Packet relayRequest = MultiplexProtocol.createRelayRequest(
            requestId, targetHost, targetPort, protocol);

        logger.info("创建relay请求: requestId={}, target={}:{}, protocol={}",
            requestId, targetHost, targetPort, protocol);

        // 验证relay请求包格式
        assertNotNull(relayRequest, "Relay请求包不应为null");
        assertEquals(MultiplexProtocol.TYPE_RELAY_REQUEST, relayRequest.getType(),
            "应该是relay请求类型");

        // 解析relay请求数据
        String[] relayData = MultiplexProtocol.parseRelayRequest(relayRequest);
        assertEquals(4, relayData.length, "Relay数据应该包含4个字段");
        assertEquals(requestId, relayData[0], "RequestId应该匹配");
        assertEquals(protocol, relayData[1], "Protocol应该匹配");
        assertEquals(targetHost, relayData[2], "TargetHost应该匹配");
        assertEquals(String.valueOf(targetPort), relayData[3], "TargetPort应该匹配");

        logger.info("Relay请求包解析正确: {}", java.util.Arrays.toString(relayData));

        // 这里需要实际的MultiplexSession来处理relay请求
        // 模拟relay连接建立过程

        logger.info("Relay连接建立功能测试通过 ✓");
    }

    /**
     * 测试5: 端到端集成测试
     * 验证完整的代理流程：接收请求 -> hash分配 -> 队列处理 -> 建立连接 -> 数据转发
     */
    @Test
    @Order(5)
    void testEndToEndProxyFlow() throws Exception {
        logger.info("=== 测试端到端集成功能 ===");

        // 创建多个并发请求来测试完整流程
        int concurrentRequests = 10;
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        CountDownLatch latch = new CountDownLatch(concurrentRequests);

        for (int i = 0; i < concurrentRequests; i++) {
            final int requestIndex = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 创建测试请求
                    String host = "httpbin.org";
                    int port = 80;
                    String requestId = "e2e-test-" + requestIndex;

                    ProxyRequest request = ProxyRequest.builder()
                        .requestId(requestId)
                        .target(host, port)
                        .protocol("HTTP")
                        .clientId("e2e-client-" + requestIndex)
                        .clientChannel(new EmbeddedChannel())
                        .data(Unpooled.copiedBuffer("GET /ip HTTP/1.1\r\nHost: " + host + "\r\n\r\n",
                            java.nio.charset.StandardCharsets.UTF_8))
                        .build();

                    // 验证hash分配
                    int queueIndex = ConnectionKeyUtils.calculateQueueIndex(request, 4);
                    logger.debug("请求 {} 分配到队列 {}", requestId, queueIndex);

                    // 这里需要实际处理请求
                    // CompletableFuture<ProxyResponse> responseFuture = proxyProcessor.processRequest(request);
                    // ProxyResponse response = responseFuture.get(30, TimeUnit.SECONDS);

                    // 验证处理结果
                    // assertTrue(response.isSuccess(), "请求应该成功处理: " + requestId);

                    logger.info("端到端请求 {} 处理完成", requestId);

                } catch (Exception e) {
                    logger.error("端到端测试请求 {} 失败", requestIndex, e);
                } finally {
                    latch.countDown();
                }
            });

            futures.add(future);
        }

        // 等待所有请求完成
        assertTrue(latch.await(60, TimeUnit.SECONDS), "所有端到端请求应该在60秒内完成");

        // 等待所有Future完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(10, TimeUnit.SECONDS);

        logger.info("端到端集成功能测试通过 ✓");
    }

    /**
     * 测试6: 性能和稳定性测试
     * 验证系统在高负载下的表现
     */
    @Test
    @Order(6)
    void testPerformanceAndStability() throws Exception {
        logger.info("=== 测试性能和稳定性 ===");

        int totalRequests = 100;
        int concurrentThreads = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(concurrentThreads);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        // 启动并发测试线程
        for (int t = 0; t < concurrentThreads; t++) {
            final int threadId = t;
            new Thread(() -> {
                try {
                    startLatch.await(); // 等待统一开始

                    int requestsPerThread = totalRequests / concurrentThreads;
                    for (int i = 0; i < requestsPerThread; i++) {
                        try {
                            String requestId = "perf-test-" + threadId + "-" + i;

                            // 创建性能测试请求
                            ProxyRequest request = ProxyRequest.builder()
                                .requestId(requestId)
                                .target("httpbin.org", 80)
                                .protocol("HTTP")
                                .clientId("perf-client-" + threadId)
                                .clientChannel(new EmbeddedChannel())
                                .data(Unpooled.copiedBuffer("GET /ip HTTP/1.1\r\nHost: httpbin.org\r\n\r\n",
                                    java.nio.charset.StandardCharsets.UTF_8))
                                .build();

                            // 验证hash分配一致性
                            int queueIndex1 = ConnectionKeyUtils.calculateQueueIndex(request, 4);
                            int queueIndex2 = ConnectionKeyUtils.calculateQueueIndex(request, 4);
                            assertEquals(queueIndex1, queueIndex2, "Hash分配应该保持一致");

                            successCount.incrementAndGet();

                        } catch (Exception e) {
                            logger.error("性能测试请求失败: thread={}, request={}", threadId, i, e);
                            errorCount.incrementAndGet();
                        }
                    }

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    finishLatch.countDown();
                }
            }).start();
        }

        // 开始测试
        startLatch.countDown();

        // 等待完成
        assertTrue(finishLatch.await(120, TimeUnit.SECONDS), "性能测试应该在120秒内完成");

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 计算性能指标
        double successRate = (double) successCount.get() / totalRequests * 100;
        double requestsPerSecond = (double) totalRequests / (duration / 1000.0);

        logger.info("性能测试结果:");
        logger.info("总请求数: {}", totalRequests);
        logger.info("成功请求数: {}", successCount.get());
        logger.info("失败请求数: {}", errorCount.get());
        logger.info("成功率: {:.2f}%", successRate);
        logger.info("耗时: {}ms", duration);
        logger.info("QPS: {:.2f}", requestsPerSecond);

        // 验证性能指标
        assertTrue(successRate >= 95.0, "成功率应该至少95%");
        assertTrue(requestsPerSecond > 10, "QPS应该大于10");

        logger.info("性能和稳定性测试通过 ✓");
    }
}
