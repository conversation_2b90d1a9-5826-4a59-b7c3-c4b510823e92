package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.protocol.MultiplexProtocol;
import com.xiang.proxy.server.diagnostic.DataLossDiagnostic;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

/**
 * 多路复用后端数据路由器
 * 支持动态会话路由，解决队列模式下的数据路由问题
 */
public class MultiplexBackendDataRouter extends ChannelInboundHandlerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexBackendDataRouter.class);
    
    // 全局会话路由表：连接ID -> 会话路由信息
    private static final ConcurrentHashMap<String, SessionRouteInfo> globalSessionRoutes = new ConcurrentHashMap<>();

    // 预注册路由表：连接ID -> 会话路由信息（用于解决时序问题）
    private static final ConcurrentHashMap<String, SessionRouteInfo> preRegisteredRoutes = new ConcurrentHashMap<>();
    
    private final String connectionId;
    private volatile boolean errorHandled = false;
    
    // 数据包统计
    private volatile long packetsReceived = 0;
    private volatile long packetsForwarded = 0;
    private volatile long packetsDropped = 0;
    
    public MultiplexBackendDataRouter(String connectionId) {
        this.connectionId = connectionId;
    }
    
    /**
     * 预注册会话路由信息（在连接建立前）
     */
    public static void preRegisterSessionRoute(String connectionId, int sessionId, Channel clientChannel,
                                             BiConsumer<Integer, String> errorHandler) {
        SessionRouteInfo routeInfo = new SessionRouteInfo(sessionId, clientChannel, errorHandler);
        preRegisteredRoutes.put(connectionId, routeInfo);

        logger.debug("预注册会话路由: connectionId={}, sessionId={}, 预注册表大小={}",
                    connectionId, sessionId, preRegisteredRoutes.size());
    }

    /**
     * 激活预注册的会话路由（连接建立成功后）
     */
    public static void activateSessionRoute(String connectionId) {
        SessionRouteInfo routeInfo = preRegisteredRoutes.remove(connectionId);
        if (routeInfo != null) {
            SessionRouteInfo existing = globalSessionRoutes.put(connectionId, routeInfo);

            if (existing != null) {
                logger.warn("覆盖已存在的会话路由: connectionId={}, 旧sessionId={}, 新sessionId={}",
                           connectionId, existing.sessionId, routeInfo.sessionId);
            }

            logger.debug("激活会话路由: connectionId={}, sessionId={}, 路由表大小={}",
                        connectionId, routeInfo.sessionId, globalSessionRoutes.size());

            // 诊断工具：记录路由注册
            DataLossDiagnostic.getInstance().recordRouteRegistration(connectionId, routeInfo.sessionId);
        } else {
            logger.warn("未找到预注册的路由信息: connectionId={}", connectionId);
        }
    }

    /**
     * 注册会话路由信息（兼容原有接口）
     */
    public static void registerSessionRoute(String connectionId, int sessionId, Channel clientChannel,
                                          BiConsumer<Integer, String> errorHandler) {
        SessionRouteInfo routeInfo = new SessionRouteInfo(sessionId, clientChannel, errorHandler);
        SessionRouteInfo existing = globalSessionRoutes.put(connectionId, routeInfo);

        if (existing != null) {
            logger.warn("覆盖已存在的会话路由: connectionId={}, 旧sessionId={}, 新sessionId={}",
                       connectionId, existing.sessionId, sessionId);
        }

        logger.debug("注册会话路由: connectionId={}, sessionId={}, 路由表大小={}",
                    connectionId, sessionId, globalSessionRoutes.size());

        // 诊断工具：记录路由注册
        DataLossDiagnostic.getInstance().recordRouteRegistration(connectionId, sessionId);
    }
    
    /**
     * 清理预注册的路由（连接建立失败时）
     */
    public static void cleanupPreRegisteredRoute(String connectionId) {
        SessionRouteInfo removed = preRegisteredRoutes.remove(connectionId);
        if (removed != null) {
            logger.debug("清理预注册路由: connectionId={}, sessionId={}", connectionId, removed.sessionId);
        }
    }

    /**
     * 注销会话路由信息
     */
    public static void unregisterSessionRoute(String connectionId) {
        // 先尝试从活跃路由表中移除
        SessionRouteInfo removed = globalSessionRoutes.remove(connectionId);
        if (removed != null) {
            logger.debug("注销会话路由: connectionId={}, sessionId={}", connectionId, removed.sessionId);

            // 诊断工具：记录路由注销
            DataLossDiagnostic.getInstance().recordRouteUnregistration(connectionId);
        } else {
            // 如果活跃路由表中没有，尝试从预注册表中清理
            cleanupPreRegisteredRoute(connectionId);
        }
    }
    
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (!(msg instanceof ByteBuf)) {
            logger.warn("收到非ByteBuf消息: connectionId={}, msgType={}", connectionId, msg.getClass());
            return;
        }

        ByteBuf data = (ByteBuf) msg;
        boolean dataProcessed = false;
        long startTime = System.currentTimeMillis();

        try {
            // 统计接收到的数据包
            packetsReceived++;

            // 增强日志：记录接收到的数据包详情
            logger.debug("后端数据接收: connectionId={}, bytes={}, 总接收包数={}",
                        connectionId, data.readableBytes(), packetsReceived);

            // 诊断工具：记录数据接收
            DataLossDiagnostic.getInstance().recordDataReceived(connectionId, data.readableBytes());

            // 获取会话路由信息
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo == null) {
                logger.warn("未找到会话路由信息: connectionId={}, 丢弃数据, bytes={}, 当前路由表大小={}",
                           connectionId, data.readableBytes(), globalSessionRoutes.size());
                packetsDropped++;

                // 诊断工具：记录路由未命中
                DataLossDiagnostic.getInstance().recordRouteMiss(connectionId, data.readableBytes());

                // 诊断信息：打印当前路由表的所有键
                if (logger.isDebugEnabled()) {
                    logger.debug("当前路由表键值: {}", globalSessionRoutes.keySet());
                }
                return;
            }
            
            // 检查客户端连接状态
            if (routeInfo.clientChannel == null || !routeInfo.clientChannel.isActive()) {
                logger.warn("客户端连接不可用，丢弃后端数据: connectionId={}, sessionId={}, bytes={}", 
                           connectionId, routeInfo.sessionId, data.readableBytes());
                packetsDropped++;
                routeInfo.errorHandler.accept(routeInfo.sessionId, "客户端连接不可用");
                return;
            }
            
            // 检查数据是否有效
            if (!data.isReadable()) {
                logger.debug("收到空数据: connectionId={}, sessionId={}", connectionId, routeInfo.sessionId);
                packetsDropped++;
                return;
            }
            
            // 零拷贝优化：创建多路复用数据包
            MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createZeroCopyDataPacket(
                routeInfo.sessionId, data, true);
            ByteBuf packetBuffer = dataPacket.encode();
            
            // 标记数据已被处理
            dataProcessed = true;
            
            // 转发数据到客户端
            routeInfo.clientChannel.writeAndFlush(packetBuffer).addListener(future -> {
                long processingTime = System.currentTimeMillis() - startTime;
                if (!future.isSuccess()) {
                    packetsDropped++;

                    logger.error("转发后端数据到客户端失败: connectionId={}, sessionId={}, 处理时间={}ms",
                               connectionId, routeInfo.sessionId, processingTime, future.cause());
                    routeInfo.errorHandler.accept(routeInfo.sessionId, "转发数据失败: " + future.cause().getMessage());
                } else {
                    packetsForwarded++;

                    if (logger.isDebugEnabled()) {
                        logger.debug("转发后端数据到客户端成功: connectionId={}, sessionId={}, bytes={}, 处理时间={}ms, 转发率={:.2f}%",
                                   connectionId, routeInfo.sessionId, data.readableBytes(), processingTime,
                                   packetsReceived > 0 ? (packetsForwarded * 100.0 / packetsReceived) : 0.0);
                    }

                    // 性能监控：记录处理延迟
                    if (processingTime > 100) {
                        logger.warn("数据转发延迟较高: connectionId={}, sessionId={}, 处理时间={}ms",
                                   connectionId, routeInfo.sessionId, processingTime);
                    }
                }
            });
            
        } catch (Exception e) {
            logger.error("处理后端数据时发生异常: connectionId={}", connectionId, e);
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo != null) {
                routeInfo.errorHandler.accept(routeInfo.sessionId, "数据处理异常: " + e.getMessage());
            }
        } finally {
            // 只有在数据没有被处理时才释放，避免双重释放
            if (!dataProcessed) {
                data.release();
            }
        }
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        if (!errorHandled) {
            errorHandled = true;
            logger.debug("后端连接断开: connectionId={}, remote={}, 统计: 接收={}, 转发={}, 丢弃={}",
                        connectionId, ctx.channel().remoteAddress(), packetsReceived, packetsForwarded, packetsDropped);
            
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo != null) {
                routeInfo.errorHandler.accept(routeInfo.sessionId, "后端连接断开");
            }
        }
        
        // 清理路由信息
        unregisterSessionRoute(connectionId);
        super.channelInactive(ctx);
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        if (!errorHandled) {
            errorHandled = true;
            
            // 根据异常类型提供更具体的错误信息
            String errorMessage;
            if (cause instanceof java.net.ConnectException) {
                errorMessage = "后端连接失败";
            } else if (cause instanceof java.io.IOException && cause.getMessage().contains("Connection reset")) {
                errorMessage = "后端连接异常: Connection reset";
            } else if (cause instanceof java.io.IOException) {
                errorMessage = "后端连接IO异常";
            } else {
                errorMessage = "后端连接异常: " + cause.getMessage();
            }
            
            logger.warn("后端连接异常: connectionId={}, error={}", connectionId, errorMessage);
            
            SessionRouteInfo routeInfo = globalSessionRoutes.get(connectionId);
            if (routeInfo != null) {
                routeInfo.errorHandler.accept(routeInfo.sessionId, errorMessage);
            }
        }
        
        // 清理路由信息
        unregisterSessionRoute(connectionId);
        super.exceptionCaught(ctx, cause);
    }
    
    /**
     * 会话路由信息
     */
    private static class SessionRouteInfo {
        final int sessionId;
        final Channel clientChannel;
        final BiConsumer<Integer, String> errorHandler;
        
        SessionRouteInfo(int sessionId, Channel clientChannel, BiConsumer<Integer, String> errorHandler) {
            this.sessionId = sessionId;
            this.clientChannel = clientChannel;
            this.errorHandler = errorHandler;
        }
    }
    
    /**
     * 获取统计信息
     */
    public String getPacketStats() {
        return String.format("接收=%d, 转发=%d, 丢弃=%d", packetsReceived, packetsForwarded, packetsDropped);
    }
}
