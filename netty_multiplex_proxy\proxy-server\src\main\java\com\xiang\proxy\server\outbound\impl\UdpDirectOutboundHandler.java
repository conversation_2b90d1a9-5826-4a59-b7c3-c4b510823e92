package com.xiang.proxy.server.outbound.impl;

import com.xiang.proxy.server.blacklist.HostBlacklist;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import com.xiang.proxy.server.outbound.OutboundConfig;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.outbound.OutboundStatistics;
import com.xiang.proxy.server.outbound.OutboundHandlerType;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollDatagramChannel;
import io.netty.channel.socket.DatagramChannel;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.channel.socket.DatagramPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * UDP直连Outbound处理器
 * 直接连接到目标服务器
 */
public class UdpDirectOutboundHandler implements OutboundHandler {
    private static final Logger logger = LoggerFactory.getLogger(UdpDirectOutboundHandler.class);

    private final String outboundId;
    private final OutboundConfig config;
    private final AtomicLong connectionCounter = new AtomicLong(0);
    private final AtomicLong successCounter = new AtomicLong(0);
    private final AtomicLong failureCounter = new AtomicLong(0);
    private volatile HealthStatus healthStatus = HealthStatus.HEALTHY;

    // 指标收集实例
    private final AdvancedMetrics advancedMetrics = AdvancedMetrics.getInstance();
    private final PerformanceMetrics performanceMetrics = PerformanceMetrics.getInstance();
    
    // UDP Bootstrap模板，避免每次连接都创建新对象
    private final Bootstrap udpBootstrapTemplate;

    public UdpDirectOutboundHandler(String outboundId, OutboundConfig config) {
        this.outboundId = outboundId;
        this.config = config != null ? config : new OutboundConfig();
        this.udpBootstrapTemplate = createUdpBootstrapTemplate();
    }
    
    /**
     * 创建UDP Bootstrap模板
     * 预配置所有通用选项，避免每次连接都重复配置
     */
    private Bootstrap createUdpBootstrapTemplate() {
        //PlatformDependent.isWindows()
        Class<? extends DatagramChannel> datagramChannelClass = NioDatagramChannel.class;
         if(Epoll.isAvailable()) {
            datagramChannelClass= EpollDatagramChannel.class;
        }

        Bootstrap template = new Bootstrap();
        template.channel(datagramChannelClass)
                .option(ChannelOption.SO_RCVBUF, config.getReceiveBufferSize())
                .option(ChannelOption.SO_SNDBUF, config.getSendBufferSize())
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK,
                        new WriteBufferWaterMark(config.getLowWaterMark(), config.getHighWaterMark()))
                .handler(new ChannelInboundHandlerAdapter() {
                    @Override
                    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
                        // UDP响应处理将在sendData方法中设置具体的处理逻辑
                        if (msg instanceof DatagramPacket) {
                            DatagramPacket responsePacket = (DatagramPacket) msg;
                            logger.debug("收到UDP响应: {} bytes from {}",
                                    responsePacket.content().readableBytes(), responsePacket.sender());
                            // 这里可以添加响应处理逻辑，但通常UDP是无连接的，响应处理在上层
                        }
                        super.channelRead(ctx, msg);
                    }

                    @Override
                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                        logger.error("UDP连接异常: {}", cause.getMessage());
                        
                        // 记录错误
                        advancedMetrics.recordError("udp_connection_exception");
                        performanceMetrics.incrementConnectionErrors();
                        
                        updateHealthStatus(false);
                    }
                });
        return template;
    }

    @Override
    public CompletableFuture<OutboundConnection> connect(ProxyRequest request) {
        CompletableFuture<OutboundConnection> future = new CompletableFuture<>();
        long startTime = System.currentTimeMillis();
        connectionCounter.incrementAndGet();

        // 记录请求和协议统计
        advancedMetrics.recordRequest();
        advancedMetrics.recordProtocolUsage("UDP");
        performanceMetrics.incrementUdpConnections();
        performanceMetrics.incrementTotalConnections();

        logger.debug("开始建立UDP直连连接: {} -> {}:{}",
                request.getRequestId(), request.getTargetHost(), request.getTargetPort());

        // 直接创建新的UDP连接（已移除ConnectionPool）
        createNewUdpConnection(request, future, startTime);
        return future;
    }

    /**
     * 创建新的UDP连接
     * 使用UDP Bootstrap模板克隆，避免重复创建和配置
     */
    private void createNewUdpConnection(ProxyRequest request, CompletableFuture<OutboundConnection> future,
            long startTime) {
        EventLoopGroup eventLoopGroup = request.getClientChannel().eventLoop();
        
        // 克隆UDP Bootstrap模板并设置EventLoopGroup
        Bootstrap udpBootstrap = udpBootstrapTemplate.clone().group(eventLoopGroup);

        // 绑定本地端口（0表示系统自动分配）
        ChannelFuture bindFuture = udpBootstrap.bind(0);

        bindFuture.addListener((ChannelFutureListener) channelFuture -> {
            long connectTime = System.currentTimeMillis() - startTime;

            if (channelFuture.isSuccess()) {
                Channel udpChannel = channelFuture.channel();
                logger.debug("UDP连接建立成功: {} -> {}:{}, 耗时: {}ms",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(), connectTime);

                try {
                    OutboundConnection connection = createOutboundConnection(udpChannel, request, startTime);
                    connection.setAttribute(OutboundConnection.Attributes.CONNECT_TIME, connectTime);

                    // 记录连接成功指标
                    advancedMetrics.recordLatency("udp_connect", connectTime);
                    advancedMetrics.recordConnectionQuality(request.getTargetHost(), true, connectTime);
                    advancedMetrics.recordResponse();
                    advancedMetrics.recordPoolCreation();
                    performanceMetrics.recordResponseTime(connectTime);

                    // 记录主机连接成功，清除黑名单记录
                    HostBlacklist.getInstance().recordSuccess(request.getTargetHost());

                    future.complete(connection);
                    successCounter.incrementAndGet();
                    updateHealthStatus(true);

                } catch (Exception e) {
                    logger.error("创建UDP OutboundConnection失败: {}", request.getRequestId(), e);

                    // 记录错误指标
                    advancedMetrics.recordError("udp_connection_creation_failed");
                    advancedMetrics.recordConnectionQuality(request.getTargetHost(), false, connectTime);
                    performanceMetrics.incrementConnectionErrors();

                    // 记录主机连接失败，用于黑名单管理
                    HostBlacklist.getInstance().recordFailure(request.getTargetHost());

                    udpChannel.close();
                    future.completeExceptionally(e);
                    failureCounter.incrementAndGet();
                    updateHealthStatus(false);
                }

            } else {
                Throwable cause = channelFuture.cause();
                logger.warn("UDP连接建立失败: {} -> {}:{}, 耗时: {}ms, 原因: {}",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(),
                        connectTime, cause.getMessage());

                // 记录连接失败指标
                advancedMetrics.recordError("udp_connection_failed");
                advancedMetrics.recordConnectionQuality(request.getTargetHost(), false, connectTime);
                performanceMetrics.incrementConnectionErrors();

                // 记录主机连接失败，用于黑名单管理
                HostBlacklist.getInstance().recordFailure(request.getTargetHost());

                future.completeExceptionally(cause);
                failureCounter.incrementAndGet();
                updateHealthStatus(false);
            }
        });
    }

    /**
     * 创建OutboundConnection对象
     */
    private OutboundConnection createOutboundConnection(Channel udpChannel, ProxyRequest request, long startTime) {
        return OutboundConnection.builder()
                .backendChannel(udpChannel)
                .target(request.getTargetHost(), request.getTargetPort())
                .protocol(request.getProtocol())
                .createTime(startTime)
                .attribute(OutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                .attribute(OutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                .attribute(OutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                .build();
    }



    @Override
    public CompletableFuture<Void> sendData(OutboundConnection connection, ByteBuf data) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        long startTime = System.currentTimeMillis();

        // 验证连接状态
        if (connection == null) {
            advancedMetrics.recordError("udp_send_null_connection");
            future.completeExceptionally(new IllegalArgumentException("Connection is null"));
            return future;
        }

        if (!connection.isActive()) {
            advancedMetrics.recordError("udp_send_inactive_connection");
            future.completeExceptionally(new IllegalStateException("Connection is not active"));
            return future;
        }

        Channel udpChannel = connection.getBackendChannel();
        if (udpChannel == null) {
            logger.warn("UDP连接通道为空: {}", connection.getConnectionId());
            connection.markInactive();
            advancedMetrics.recordError("udp_send_null_channel");
            future.completeExceptionally(new IllegalStateException("UDP channel is null"));
            return future;
        }

        if (!udpChannel.isActive()) {
            logger.warn("UDP连接通道已关闭: {}", connection.getConnectionId());
            connection.markInactive();
            advancedMetrics.recordError("udp_send_inactive_channel");
            future.completeExceptionally(new IllegalStateException("UDP channel is not active"));
            return future;
        }

        // 验证数据
        if (data == null || !data.isReadable()) {
            logger.debug("UDP数据为空或不可读: {}", connection.getConnectionId());
            future.complete(null);
            return future;
        }

        // 创建UDP数据包并发送
        int dataSize = data.readableBytes();
        data.retain(); // 保留数据引用

        try {
            // 创建目标地址
            InetSocketAddress targetAddress = new InetSocketAddress(
                    connection.getTargetHost(), connection.getTargetPort());

            // 创建UDP数据包
            DatagramPacket udpPacket = new DatagramPacket(data, targetAddress);

            ChannelFuture writeFuture = udpChannel.writeAndFlush(udpPacket);

            writeFuture.addListener((ChannelFutureListener) channelFuture -> {
                try {
                    long sendTime = System.currentTimeMillis() - startTime;
                    
                    if (channelFuture.isSuccess()) {
                        connection.addBytesTransferred(dataSize);
                        
                        // 记录发送成功指标
                        advancedMetrics.recordLatency("udp_send", sendTime);
                        advancedMetrics.recordBytesSent(dataSize);
                        performanceMetrics.addBytesTransferred(dataSize);
                        
                        logger.debug("UDP数据发送成功: {}, bytes={}, target={}:{}, 耗时: {}ms",
                                connection.getConnectionId(), dataSize,
                                connection.getTargetHost(), connection.getTargetPort(), sendTime);
                        future.complete(null);
                    } else {
                        Throwable cause = channelFuture.cause();
                        
                        // 记录发送失败指标
                        advancedMetrics.recordError("udp_send_failed");
                        advancedMetrics.recordConnectionQuality(connection.getTargetHost(), false, sendTime);
                        
                        logger.error("发送UDP数据失败: {}, bytes={}, target={}:{}, 耗时: {}ms",
                                connection.getConnectionId(), dataSize,
                                connection.getTargetHost(), connection.getTargetPort(), sendTime, cause);
                        connection.markInactive();
                        future.completeExceptionally(cause);
                    }
                } finally {
                    data.release(); // 确保释放引用
                }
            });

        } catch (Exception e) {
            data.release(); // 发生异常时释放引用
            
            // 记录异常指标
            advancedMetrics.recordError("udp_send_exception");
            performanceMetrics.incrementConnectionErrors();
            
            logger.error("发送UDP数据时发生异常: {}", connection.getConnectionId(), e);
            connection.markInactive();
            future.completeExceptionally(e);
        }

        return future;
    }

    @Override
    public CompletableFuture<Void> closeConnection(OutboundConnection connection) {
        CompletableFuture<Void> future = new CompletableFuture<>();

        if (connection == null) {
            future.complete(null);
            return future;
        }

        connection.markInactive();
        Channel udpChannel = connection.getBackendChannel();

        if (udpChannel != null && udpChannel.isActive()) {
            // 直接关闭UDP连接（已移除ConnectionPool）
            ChannelFuture closeFuture = udpChannel.close();
            closeFuture.addListener((ChannelFutureListener) channelFuture -> {
                if (channelFuture.isSuccess()) {
                    logger.debug("UDP连接关闭成功: {}", connection.getConnectionId());
                    future.complete(null);
                } else {
                    logger.warn("UDP连接关闭失败: {}", connection.getConnectionId(), channelFuture.cause());
                    future.completeExceptionally(channelFuture.cause());
                }
            });
        } else {
            future.complete(null);
        }

        return future;
    }

    @Override
    public String getOutboundId() {
        return outboundId;
    }

    @Override
    public OutboundConfig getConfig() {
        return config;
    }

    @Override
    public String getType() {
        return OutboundHandlerType.UDP_DIRECT.getType();
    }

    @Override
    public HealthStatus getHealthStatus() {
        return healthStatus;
    }

    @Override
    public CompletableFuture<HealthStatus> healthCheck() {
        // 简单的健康检查：基于成功率
        long total = successCounter.get() + failureCounter.get();
        if (total == 0) {
            return CompletableFuture.completedFuture(HealthStatus.HEALTHY);
        }

        double successRate = (double) successCounter.get() / total;
        HealthStatus status;

        if (successRate >= 0.9) {
            status = HealthStatus.HEALTHY;
        } else if (successRate >= 0.5) {
            status = HealthStatus.DEGRADED;
        } else {
            status = HealthStatus.UNHEALTHY;
        }

        this.healthStatus = status;
        return CompletableFuture.completedFuture(status);
    }

    @Override
    public OutboundStatistics getStatistics() {
        return new OutboundStatistics(
                connectionCounter.get(),
                successCounter.get(),
                failureCounter.get(),
                0 // UDP连接数由连接池统计
        );
    }

    @Override
    public void resetStatistics() {
        connectionCounter.set(0);
        successCounter.set(0);
        failureCounter.set(0);
    }

    @Override
    public void destroy() {
        // UDP连接由连接池统一管理，这里只需要重置统计信息
        resetStatistics();
        logger.info("UDP直连Outbound处理器已销毁: {}", outboundId);
    }

    /**
     * 更新健康状态
     */
    private void updateHealthStatus(boolean success) {
        // 基于最近的连接成功率动态更新健康状态
        long total = successCounter.get() + failureCounter.get();
        if (total > 0) {
            double successRate = (double) successCounter.get() / total;

            if (successRate >= 0.9) {
                healthStatus = HealthStatus.HEALTHY;
            } else if (successRate >= 0.5) {
                healthStatus = HealthStatus.DEGRADED;
            } else {
                healthStatus = HealthStatus.UNHEALTHY;
            }
        }
    }
}