package com.xiang.proxy.server.outbound;

import com.xiang.proxy.server.diagnostic.DataLossDiagnostic;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异步出站连接实现
 * 支持连接建立过程中的数据缓存，避免竞态条件
 */
public class AsyncOutboundConnection {
    private static final Logger logger = LoggerFactory.getLogger(AsyncOutboundConnection.class);
    
    // 缓存队列的最大大小，防止内存泄漏
    private static final int MAX_QUEUE_SIZE = 1000;
    
    private final String connectionId;
    private final String targetHost;
    private final int targetPort;
    private final String protocol;
    private final long createTime;
    private final AtomicLong bytesTransferred;
    private final Map<String, Object> attributes;
    
    // 连接状态管理
    private volatile Channel backendChannel;
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final AtomicBoolean connecting = new AtomicBoolean(false);
    private volatile boolean active = true;
    private volatile long lastActiveTime;
    
    // 数据缓存队列 - 在连接建立前缓存数据
    private final Queue<ByteBuf> pendingDataQueue = new ConcurrentLinkedQueue<>();
    private final AtomicLong queuedBytes = new AtomicLong(0);
    // 缓存数据的Future队列，用于正确处理异步发送结果
    private final Queue<CompletableFuture<Void>> pendingDataFutures = new ConcurrentLinkedQueue<>();
    
    // 连接建立的Future
    private final CompletableFuture<Channel> connectionFuture = new CompletableFuture<>();
    
    private AsyncOutboundConnection(Builder builder) {
        this.connectionId = builder.connectionId != null ? builder.connectionId : UUID.randomUUID().toString();
        this.targetHost = builder.targetHost;
        this.targetPort = builder.targetPort;
        this.protocol = builder.protocol;
        this.createTime = builder.createTime > 0 ? builder.createTime : System.currentTimeMillis();
        this.bytesTransferred = new AtomicLong(0);
        this.attributes = new HashMap<>(builder.attributes);
        this.lastActiveTime = this.createTime;
        
        // 如果builder中已有channel，直接设置为已连接
        if (builder.backendChannel != null) {
            setBackendChannel(builder.backendChannel);
        }
    }
    
    /**
     * 设置后端连接通道 - 改进版本，确保线程安全
     */
    public void setBackendChannel(Channel channel) {
        if (channel == null) {
            logger.warn("尝试设置空的后端通道: {}", connectionId);
            return;
        }

        // 使用同步块确保状态变更的原子性
        synchronized (this) {
            // 检查连接是否已经被设置或失败
            if (connected.get() || connectionFuture.isDone()) {
                logger.warn("连接已经被处理，忽略重复设置: {}", connectionId);
                return;
            }

            this.backendChannel = channel;
            this.connected.set(true);
            this.connecting.set(false);
            this.connectionFuture.complete(channel);

            logger.debug("后端连接已建立: {}, 缓存数据队列大小: {}",
                    connectionId, pendingDataQueue.size());
        }

        // 在同步块外处理缓存数据，避免死锁
        flushPendingData();
    }
    
    /**
     * 标记连接正在建立中
     */
    public void markConnecting() {
        this.connecting.set(true);
        logger.debug("连接开始建立: {}", connectionId);
    }
    
    /**
     * 连接建立失败 - 改进版本，确保线程安全
     */
    public void setConnectionFailed(Throwable cause) {
        // 使用同步块确保状态变更的原子性
        synchronized (this) {
            // 检查连接是否已经被处理
            if (connected.get() || connectionFuture.isDone()) {
                logger.debug("连接已经被处理，忽略失败通知: {}", connectionId);
                return;
            }

            this.connecting.set(false);
            this.connectionFuture.completeExceptionally(cause);

            logger.warn("连接建立失败: {}, 缓存数据队列大小: {}",
                    connectionId, pendingDataQueue.size(), cause);
        }

        // 在同步块外清理缓存数据，避免死锁
        clearPendingData();
    }
    
    /**
     * 发送数据 - 改进版本，更好地处理并发状态变化
     */
    public CompletableFuture<Void> sendData(ByteBuf data) {
        if (!active) {
            ReferenceCountUtil.release(data);
            return CompletableFuture.failedFuture(new IllegalStateException("连接已关闭"));
        }

        // 使用同步块确保状态检查和操作的原子性
        synchronized (this) {
            // 如果连接已建立且通道可用，直接发送
            if (connected.get() && backendChannel != null && backendChannel.isActive()) {
                return sendDataDirectly(data);
            }

            // 如果连接正在建立中或未建立，缓存数据
            if (connecting.get() || !connected.get()) {
                return cacheData(data);
            }

            // 连接状态异常（理论上不应该到达这里）
            logger.warn("连接状态异常: connected={}, connecting={}, backendChannel={}, active={}",
                    connected.get(), connecting.get(), backendChannel != null, active);
            ReferenceCountUtil.release(data);
            return CompletableFuture.failedFuture(new IllegalStateException("连接状态异常"));
        }
    }
    
    /**
     * 直接发送数据
     */
    private CompletableFuture<Void> sendDataDirectly(ByteBuf data) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        
        int dataSize = data.readableBytes();

        // 诊断工具：记录数据发送
        DataLossDiagnostic.getInstance().recordDataSent(connectionId, dataSize);

        ChannelFuture channelFuture = backendChannel.writeAndFlush(data);
        channelFuture.addListener(f -> {
            if (f.isSuccess()) {
                bytesTransferred.addAndGet(dataSize);
                markActive();
                future.complete(null);
            } else {
                logger.warn("发送数据失败: {}", connectionId, f.cause());
                future.completeExceptionally(f.cause());
            }
        });
        
        return future;
    }
    
    /**
     * 缓存数据到队列 - 优化版本，正确处理异步发送结果
     */
    private CompletableFuture<Void> cacheData(ByteBuf data) {
        // 检查队列大小限制
        if (pendingDataQueue.size() >= MAX_QUEUE_SIZE) {
            ReferenceCountUtil.release(data);
            return CompletableFuture.failedFuture(new IllegalStateException("缓存队列已满"));
        }

        // 创建一个Future来跟踪这个数据包的发送状态
        CompletableFuture<Void> dataFuture = new CompletableFuture<>();

        // 增加引用计数，防止数据被释放
        data.retain();
        pendingDataQueue.offer(data);
        pendingDataFutures.offer(dataFuture);
        queuedBytes.addAndGet(data.readableBytes());

        logger.debug("数据已缓存: {}, 队列大小: {}, 缓存字节数: {}",
                connectionId, pendingDataQueue.size(), queuedBytes.get());

        // 返回这个数据包的Future，只有当数据真正发送成功后才会完成
        return dataFuture;
    }
    
    /**
     * 处理缓存的数据 - 优化版本，批量发送减少系统调用，正确处理Future
     */
    public void flushPendingData() {
        if (backendChannel == null || !backendChannel.isActive()) {
            logger.warn("后端连接不可用，无法处理缓存数据: {}", connectionId);
            clearPendingData();
            return;
        }

        int flushedCount = 0;
        long flushedBytes = 0;

        // 优化：批量处理数据，减少writeAndFlush调用次数
        ByteBuf data;
        while ((data = pendingDataQueue.poll()) != null) {
            CompletableFuture<Void> dataFuture = pendingDataFutures.poll();

            try {
                if (data.isReadable()) {
                    // 使用write而不是writeAndFlush，最后统一flush
                    ChannelFuture writeFuture = backendChannel.write(data);
                    flushedBytes += data.readableBytes();
                    flushedCount++;

                    // 正确处理每个数据包的发送结果
                    if (dataFuture != null) {
                        writeFuture.addListener(f -> {
                            if (f.isSuccess()) {
                                dataFuture.complete(null);
                            } else {
                                dataFuture.completeExceptionally(f.cause());
                            }
                        });
                    }
                } else {
                    ReferenceCountUtil.release(data);
                    if (dataFuture != null) {
                        dataFuture.complete(null); // 空数据包直接完成
                    }
                }
            } catch (Exception e) {
                logger.warn("处理缓存数据时发生异常: {}", connectionId, e);
                ReferenceCountUtil.release(data);
                if (dataFuture != null) {
                    dataFuture.completeExceptionally(e);
                }
            }
        }

        // 统一flush所有写入的数据
        if (flushedCount > 0) {
            backendChannel.flush();
            bytesTransferred.addAndGet(flushedBytes);
            queuedBytes.set(0);
            markActive();
            logger.debug("批量处理缓存数据完成: {}, 发送 {} 个数据包, {} 字节",
                    connectionId, flushedCount, flushedBytes);
        }
    }
    
    /**
     * 清理缓存的数据 - 优化版本，同时清理Future队列
     */
    private void clearPendingData() {
        int clearedCount = 0;
        long clearedBytes = queuedBytes.get();

        // 清理数据队列
        ByteBuf data;
        while ((data = pendingDataQueue.poll()) != null) {
            ReferenceCountUtil.release(data);
            clearedCount++;
        }

        // 清理Future队列，标记为失败
        CompletableFuture<Void> dataFuture;
        while ((dataFuture = pendingDataFutures.poll()) != null) {
            dataFuture.completeExceptionally(new IllegalStateException("连接已关闭，数据被清理"));
        }

        queuedBytes.set(0);

        if (clearedCount > 0) {
            logger.debug("清理缓存数据: {}, 清理 {} 个数据包, {} 字节",
                    connectionId, clearedCount, clearedBytes);
        }
    }
    
    /**
     * 关闭连接
     */
    public CompletableFuture<Void> close() {
        if (!active) {
            return CompletableFuture.completedFuture(null);
        }
        
        active = false;
        
        // 清理缓存数据
        clearPendingData();
        
        // 关闭后端连接
        if (backendChannel != null && backendChannel.isActive()) {
            CompletableFuture<Void> future = new CompletableFuture<>();
            backendChannel.close().addListener(f -> {
                if (f.isSuccess()) {
                    future.complete(null);
                } else {
                    future.completeExceptionally(f.cause());
                }
            });
            return future;
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    // Getters
    public String getConnectionId() {
        return connectionId;
    }
    
    public Channel getBackendChannel() {
        return backendChannel;
    }
    
    public String getTargetHost() {
        return targetHost;
    }
    
    public int getTargetPort() {
        return targetPort;
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public long getCreateTime() {
        return createTime;
    }
    
    public long getBytesTransferred() {
        return bytesTransferred.get();
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public boolean isActive() {
        return active && (connected.get() || connecting.get());
    }
    
    public boolean isConnected() {
        return connected.get() && backendChannel != null && backendChannel.isActive();
    }
    
    public boolean isConnecting() {
        return connecting.get();
    }
    
    public long getLastActiveTime() {
        return lastActiveTime;
    }
    
    public int getPendingDataCount() {
        return pendingDataQueue.size();
    }
    
    public long getQueuedBytes() {
        return queuedBytes.get();
    }

    /**
     * 获取连接建立的Future
     * 用于等待异步连接建立完成
     */
    public CompletableFuture<Channel> getConnectionFuture() {
        return connectionFuture;
    }
    
    // 状态管理方法
    public void markActive() {
        this.lastActiveTime = System.currentTimeMillis();
    }
    
    public void markInactive() {
        this.active = false;
    }
    
    // 便捷方法
    public String getTarget() {
        return targetHost + ":" + targetPort;
    }
    
    public long getConnectionAge() {
        return System.currentTimeMillis() - createTime;
    }
    
    public long getIdleTime() {
        return System.currentTimeMillis() - lastActiveTime;
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }
    
    public <T> T getAttribute(String key, T defaultValue) {
        T value = getAttribute(key);
        return value != null ? value : defaultValue;
    }
    
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }

    /**
     * 检查连接健康状态
     */
    public boolean isHealthy() {
        if (!active) return false;
        if (backendChannel == null) return connecting.get(); // 连接中认为是健康的
        return backendChannel.isActive() && backendChannel.isWritable();
    }

    /**
     * 获取连接统计信息
     */
    public String getConnectionStats() {
        long uptime = System.currentTimeMillis() - createTime;
        long idleTime = getIdleTime();

        return String.format("Connection[%s] - 目标: %s:%d, 协议: %s, 运行时间: %dms, " +
                "空闲时间: %dms, 传输字节: %d, 缓存数据: %d包/%d字节, 状态: %s",
                connectionId, targetHost, targetPort, protocol, uptime, idleTime,
                bytesTransferred.get(), pendingDataQueue.size(), queuedBytes.get(),
                getConnectionStatus());
    }

    /**
     * 获取连接状态描述
     */
    private String getConnectionStatus() {
        if (!active) return "INACTIVE";
        if (connected.get()) return "CONNECTED";
        if (connecting.get()) return "CONNECTING";
        return "PENDING";
    }

    @Override
    public String toString() {
        return String.format("AsyncOutboundConnection{id='%s', target='%s:%d', protocol='%s', " +
                "connected=%s, connecting=%s, active=%s, pendingData=%d, queuedBytes=%d}",
                connectionId, targetHost, targetPort, protocol, 
                connected.get(), connecting.get(), active, 
                pendingDataQueue.size(), queuedBytes.get());
    }
    
    // Builder模式
    public static class Builder {
        private String connectionId;
        private Channel backendChannel;
        private String targetHost;
        private int targetPort;
        private String protocol;
        private long createTime;
        private Map<String, Object> attributes = new HashMap<>();
        
        public Builder connectionId(String connectionId) {
            this.connectionId = connectionId;
            return this;
        }
        
        public Builder backendChannel(Channel channel) {
            this.backendChannel = channel;
            return this;
        }
        
        public Builder target(String host, int port) {
            this.targetHost = host;
            this.targetPort = port;
            return this;
        }
        
        public Builder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }
        
        public Builder createTime(long createTime) {
            this.createTime = createTime;
            return this;
        }
        
        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }
        
        public Builder attributes(Map<String, Object> attributes) {
            this.attributes.putAll(attributes);
            return this;
        }
        
        public AsyncOutboundConnection build() {
            if (targetHost == null || targetHost.trim().isEmpty()) {
                throw new IllegalArgumentException("Target host cannot be null or empty");
            }
            if (targetPort <= 0 || targetPort > 65535) {
                throw new IllegalArgumentException("Target port must be between 1 and 65535");
            }
            if (protocol == null || protocol.trim().isEmpty()) {
                throw new IllegalArgumentException("Protocol cannot be null or empty");
            }
            
            return new AsyncOutboundConnection(this);
        }
    }
    
    // 静态工厂方法
    public static Builder builder() {
        return new Builder();
    }
    
    // 属性键常量
    public static final class Attributes {
        public static final String OUTBOUND_ID = "outbound.id";
        public static final String ROUTE_RULE_ID = "route.rule.id";
        public static final String CLIENT_CONNECTION_ID = "client.connection.id";
        public static final String REQUEST_SERIAL_ID = "request.serial.id";
        public static final String SESSION_ID = "session.id";
        public static final String RETRY_COUNT = "retry.count";
        public static final String CONNECT_TIME = "connect.time";
        public static final String FIRST_BYTE_TIME = "first.byte.time";
        public static final String ASYNC_CONNECTION= "async.connection";
    }
}
