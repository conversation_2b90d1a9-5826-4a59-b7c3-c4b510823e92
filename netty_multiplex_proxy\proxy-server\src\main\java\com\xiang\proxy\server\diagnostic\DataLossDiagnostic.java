package com.xiang.proxy.server.diagnostic;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据丢失诊断工具
 * 用于分析队列模式下的数据接收问题
 */
public class DataLossDiagnostic {
    private static final Logger logger = LoggerFactory.getLogger(DataLossDiagnostic.class);
    
    private static final DataLossDiagnostic INSTANCE = new DataLossDiagnostic();
    
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final AtomicLong lastReportTime = new AtomicLong(System.currentTimeMillis());
    
    // 统计数据
    private final AtomicLong totalConnectionsCreated = new AtomicLong(0);
    private final AtomicLong totalDataSent = new AtomicLong(0);
    private final AtomicLong totalDataReceived = new AtomicLong(0);
    private final AtomicLong routeRegistrations = new AtomicLong(0);
    private final AtomicLong routeUnregistrations = new AtomicLong(0);
    private final AtomicLong routeMisses = new AtomicLong(0);
    
    private volatile boolean diagnosticEnabled = false;
    
    private DataLossDiagnostic() {}
    
    public static DataLossDiagnostic getInstance() {
        return INSTANCE;
    }
    
    /**
     * 启动诊断
     */
    public void startDiagnostic() {
        if (diagnosticEnabled) {
            logger.info("数据丢失诊断已经在运行中");
            return;
        }
        
        diagnosticEnabled = true;
        logger.info("启动数据丢失诊断工具");
        
        // 每30秒打印一次诊断报告
        scheduler.scheduleAtFixedRate(this::printDiagnosticReport, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 停止诊断
     */
    public void stopDiagnostic() {
        if (!diagnosticEnabled) {
            return;
        }
        
        diagnosticEnabled = false;
        scheduler.shutdown();
        logger.info("停止数据丢失诊断工具");
    }
    
    /**
     * 记录连接创建
     */
    public void recordConnectionCreated(String connectionId, String target) {
        if (!diagnosticEnabled) return;
        
        totalConnectionsCreated.incrementAndGet();
        logger.debug("诊断: 连接创建 - connectionId={}, target={}, 总连接数={}", 
                    connectionId, target, totalConnectionsCreated.get());
    }
    
    /**
     * 记录数据发送
     */
    public void recordDataSent(String connectionId, int bytes) {
        if (!diagnosticEnabled) return;
        
        totalDataSent.addAndGet(bytes);
        logger.debug("诊断: 数据发送 - connectionId={}, bytes={}, 总发送字节={}", 
                    connectionId, bytes, totalDataSent.get());
    }
    
    /**
     * 记录数据接收
     */
    public void recordDataReceived(String connectionId, int bytes) {
        if (!diagnosticEnabled) return;
        
        totalDataReceived.addAndGet(bytes);
        logger.debug("诊断: 数据接收 - connectionId={}, bytes={}, 总接收字节={}", 
                    connectionId, bytes, totalDataReceived.get());
    }
    
    /**
     * 记录路由注册
     */
    public void recordRouteRegistration(String connectionId, int sessionId) {
        if (!diagnosticEnabled) return;
        
        routeRegistrations.incrementAndGet();
        logger.debug("诊断: 路由注册 - connectionId={}, sessionId={}, 总注册数={}", 
                    connectionId, sessionId, routeRegistrations.get());
    }
    
    /**
     * 记录路由注销
     */
    public void recordRouteUnregistration(String connectionId) {
        if (!diagnosticEnabled) return;
        
        routeUnregistrations.incrementAndGet();
        logger.debug("诊断: 路由注销 - connectionId={}, 总注销数={}", 
                    connectionId, routeUnregistrations.get());
    }
    
    /**
     * 记录路由未命中
     */
    public void recordRouteMiss(String connectionId, int bytes) {
        if (!diagnosticEnabled) return;
        
        routeMisses.incrementAndGet();
        logger.warn("诊断: 路由未命中 - connectionId={}, bytes={}, 总未命中数={}", 
                   connectionId, bytes, routeMisses.get());
    }
    
    /**
     * 打印诊断报告
     */
    private void printDiagnosticReport() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastReport = currentTime - lastReportTime.get();
        lastReportTime.set(currentTime);
        
        logger.info("=== 数据丢失诊断报告 ({}ms) ===", timeSinceLastReport);
        logger.info("连接统计: 创建={}", totalConnectionsCreated.get());
        logger.info("数据统计: 发送={}字节, 接收={}字节, 接收率={:.2f}%", 
                   totalDataSent.get(), totalDataReceived.get(),
                   totalDataSent.get() > 0 ? (totalDataReceived.get() * 100.0 / totalDataSent.get()) : 0.0);
        logger.info("路由统计: 注册={}, 注销={}, 未命中={}, 活跃路由={}", 
                   routeRegistrations.get(), routeUnregistrations.get(), routeMisses.get(),
                   routeRegistrations.get() - routeUnregistrations.get());
        
        // 检查异常情况
        checkAnomalies();
    }
    
    /**
     * 检查异常情况
     */
    private void checkAnomalies() {
        // 检查数据接收率
        long sent = totalDataSent.get();
        long received = totalDataReceived.get();
        if (sent > 0) {
            double receiveRate = received * 100.0 / sent;
            if (receiveRate < 50.0) {
                logger.error("异常: 数据接收率过低 {:.2f}% (发送={}字节, 接收={}字节)", 
                           receiveRate, sent, received);
            }
        }
        
        // 检查路由未命中率
        long registrations = routeRegistrations.get();
        long misses = routeMisses.get();
        if (registrations > 0) {
            double missRate = misses * 100.0 / registrations;
            if (missRate > 10.0) {
                logger.error("异常: 路由未命中率过高 {:.2f}% (注册={}, 未命中={})", 
                           missRate, registrations, misses);
            }
        }
        
        // 检查路由泄漏
        long activeRoutes = routeRegistrations.get() - routeUnregistrations.get();
        if (activeRoutes > 1000) {
            logger.warn("警告: 活跃路由数量过多 {} (可能存在路由泄漏)", activeRoutes);
        }
    }
    
    /**
     * 获取统计信息
     */
    public DiagnosticStats getStats() {
        return new DiagnosticStats(
            totalConnectionsCreated.get(),
            totalDataSent.get(),
            totalDataReceived.get(),
            routeRegistrations.get(),
            routeUnregistrations.get(),
            routeMisses.get()
        );
    }
    
    /**
     * 诊断统计信息
     */
    public static class DiagnosticStats {
        public final long totalConnectionsCreated;
        public final long totalDataSent;
        public final long totalDataReceived;
        public final long routeRegistrations;
        public final long routeUnregistrations;
        public final long routeMisses;
        
        DiagnosticStats(long totalConnectionsCreated, long totalDataSent, long totalDataReceived,
                       long routeRegistrations, long routeUnregistrations, long routeMisses) {
            this.totalConnectionsCreated = totalConnectionsCreated;
            this.totalDataSent = totalDataSent;
            this.totalDataReceived = totalDataReceived;
            this.routeRegistrations = routeRegistrations;
            this.routeUnregistrations = routeUnregistrations;
            this.routeMisses = routeMisses;
        }
        
        public double getDataReceiveRate() {
            return totalDataSent > 0 ? (totalDataReceived * 100.0 / totalDataSent) : 0.0;
        }
        
        public double getRouteMissRate() {
            return routeRegistrations > 0 ? (routeMisses * 100.0 / routeRegistrations) : 0.0;
        }
        
        public long getActiveRoutes() {
            return routeRegistrations - routeUnregistrations;
        }
    }
}
