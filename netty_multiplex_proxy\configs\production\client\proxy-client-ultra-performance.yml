# 代理客户端配置文件 - 本机部署上行速度优化版
# 参考traffic-3.0的客户端优化策略，专门针对上行速度优化

# 地址过滤模式
# ALL_PROXY: 所有连接都通过proxy-server转发
# CHINA_DIRECT: 中国地区IP直连，其他通过proxy-server转发
# ALL_DIRECT: 所有连接都直连
filter:
  mode: ALL_PROXY

# 代理服务器配置
proxy:
  server:
    host: localhost
    port: 8888

# 本地端口（向后兼容）
local:
  port: 1081

# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 60  # 增加超时时间，减少重认证开销

# 性能配置 - 针对上行速度优化（参考traffic-3.0的性能策略）
performance:
  # 工作线程数 (0表示自动计算，基于CPU核心数)
  worker-threads: 0
  # 直连工作线程数 (0表示自动计算，基于CPU核心数)
  direct-connection-threads: 0
  # I/O比例 (客户端也是I/O密集型，设置高值)
  io-ratio: 90
  # 启用线程优化
  enable-thread-optimization: true
  # 最大工作线程数 (增加以支持更高并发)
  max-worker-threads: 128
  # 最小工作线程数 (确保基础性能)
  min-worker-threads: 16

# 网络优化配置 - 参考traffic-3.0的网络优化
network:
  # 连接配置
  connection:
    # 连接超时时间 (3秒，快速失败)
    connect-timeout: 3000
    # 读取超时时间
    read-timeout: 30000
    # 写入超时时间
    write-timeout: 30000
    # 启用TCP_NODELAY，减少延迟
    tcp-no-delay: true
    # 启用SO_KEEPALIVE
    keep-alive: true
    # 启用SO_REUSEADDR
    reuse-address: true
  
  # 缓冲区配置 - 针对上行优化
  buffer:
    # 接收缓冲区大小 (1MB，提升上行数据接收)
    receive-buffer-size: 1048576
    # 发送缓冲区大小 (1MB，提升下行数据发送)
    send-buffer-size: 1048576
    # 写入缓冲区水位标记
    write-buffer-low-water-mark: 262144   # 256KB
    write-buffer-high-water-mark: 1048576 # 1MB

# 接入器配置 - 支持多个同类型接入器，优化性能
inbound:
  # SOCKS5 接入器列表
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
      # 性能优化配置
      backlog: 4096  # 增大连接队列
      max-connections: 50000  # 增大最大连接数
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"
  
  # HTTP 接入器列表
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"
      # 性能优化配置
      backlog: 4096  # 增大连接队列
      max-connections: 50000  # 增大最大连接数
    - name: "http-backup"
      port: 1085
      enabled: false
      description: "备用HTTP代理"

# SSL/TLS配置 - 客户端连接到代理服务器的SSL设置
ssl:
  enable: true
  trust-all: false
  trust-store-path: "truststore.p12"
  trust-store-password: "xiang1"
  trust-store-type: "PKCS12"
  key-store-path: "client.p12"
  key-store-password: "xiang1"
  key-store-type: "PKCS12"
  protocols:
    - "TLSv1.2"
    - "TLSv1.3"
  cipher-suites: []
  verify-hostname: false
  handshake-timeout-seconds: 30

# 队列配置 - 参考traffic-3.0的队列优化策略
queue:
  # 队列容量（增大以支持更高并发）
  capacity: 50000
  # 批处理大小（增大以提高吞吐量）
  batch-size: 200
  # 刷新间隔（减少以提高响应性）
  flush-interval-ms: 5
  # 重试配置
  retry:
    # 最大重试次数
    max-attempts: 3
    # 重试延迟（毫秒）
    delay-ms: 500  # 减少重试延迟
  # 监控配置
  monitoring:
    # 是否启用队列监控
    enabled: true
    # 监控报告间隔（秒）
    report-interval-seconds: 60  # 减少监控开销
    # 队列使用率警告阈值（百分比）
    warning-threshold: 85
    # 队列使用率错误阈值（百分比）
    error-threshold: 95

# 内存优化配置 - 参考traffic-3.0的内存管理
memory:
  # 启用内存优化
  optimization: true
  # 内存使用阈值
  threshold: 0.8
  # 缓冲区优化
  buffer-optimization: true
  # 启用零拷贝优化
  zero-copy: true

# 在线数据源配置
online-data-sources:
  # 中国IP段数据源
  china-ip-ranges:
    - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
    - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"

# Nacos 服务发现配置
nacos:
  # 是否启用 Nacos 服务发现，false则使用配置的ip和端口
  enabled: false
  serverAddr: *************:8848
  namespace: ""
  serviceName: main-multiplex
  groupName: DEFAULT_GROUP
  username: nacos
  password: nacos

# 高性能JVM参数建议 (客户端启动脚本中使用):
# 基础内存配置
# -Xms2g -Xmx4g                    # 设置合适的堆内存
# -XX:MetaspaceSize=128m           # 设置元空间
# -XX:MaxMetaspaceSize=256m        # 限制元空间最大值
# 
# 垃圾收集器优化
# -XX:+UseG1GC                     # 使用G1垃圾收集器
# -XX:MaxGCPauseMillis=50          # 客户端要求更低的GC暂停时间
# -XX:G1HeapRegionSize=8m          # 设置较小的G1区域大小
# 
# Netty优化参数 - 参考traffic-3.0
# -Dio.netty.allocator.type=pooled # 使用池化内存分配器
# -Dio.netty.allocator.maxOrder=8  # 限制最大分配块大小
# -Dio.netty.recycler.maxCapacityPerThread=0  # 禁用对象回收器
# -Dio.netty.leakDetection.level=DISABLED     # 生产环境禁用内存泄漏检测
# -Dio.netty.noUnsafe=false        # 启用Unsafe优化
# 
# 网络优化参数
# -Djava.net.preferIPv4Stack=true  # 优先使用IPv4
# -Djava.net.useSystemProxies=false # 禁用系统代理
# 
# 性能优化参数
# -XX:+UseStringDeduplication      # 启用字符串去重
# -XX:+OptimizeStringConcat        # 优化字符串连接
# -server                          # 使用服务器模式JVM
