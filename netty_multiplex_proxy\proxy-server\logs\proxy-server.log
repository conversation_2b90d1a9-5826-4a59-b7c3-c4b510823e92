2025-08-23 14:46:05.577 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:46:05.582 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:46:05.586 [main] WARN  c.x.p.s.c.ProxyServerV2ConfigManager - 配置目录中未找到YAML配置文件: /app/config/
2025-08-23 14:46:05.587 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用properties文件指定的配置文件: netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml
2025-08-23 14:46:05.588 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 未找到 YAML 配置文件，使用默认配置
2025-08-23 14:46:05.597 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用默认配置初始化
2025-08-23 14:46:05.597 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 配置加载完成 - 认证: 禁用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
2025-08-23 14:46:05.597 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 连接池配置 - 最大连接数/主机: 20, 空闲超时: 30秒, 清理间隔: 30秒
2025-08-23 14:46:05.597 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 性能监控配置 - 报告间隔: 30秒
2025-08-23 14:46:05.597 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 黑名单配置 - 失败阈值: 3, 缓存超时: 30秒
2025-08-23 14:46:05.602 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:46:05.603 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:46:05.603 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:46:05.609 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:46:05.609 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:46:05.609 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:46:05.611 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:05.611 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:05.611 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:46:05.611 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:46:05.612 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:46:05.612 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:46:05.612 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:46:05.612 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:46:05.622 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:46:05.622 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:46:05.622 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:46:05.622 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:46:05.622 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:46:05.623 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:46:05.626 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:46:05.627 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:46:05.628 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:46:05.628 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:46:05.629 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:46:07.641 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试Hash分配包到队列功能 ===
2025-08-23 14:46:07.641 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 第1轮分配测试
2025-08-23 14:46:07.649 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 第2轮分配测试
2025-08-23 14:46:07.651 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 第3轮分配测试
2025-08-23 14:46:07.651 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列分布统计:
2025-08-23 14:46:07.651 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列0: 18个连接键值
2025-08-23 14:46:07.652 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列1: 18个连接键值
2025-08-23 14:46:07.652 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列2: 14个连接键值
2025-08-23 14:46:07.652 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列3: 14个连接键值
2025-08-23 14:46:07.652 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - Hash分配包到队列功能测试通过 ✓
2025-08-23 14:46:07.654 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:46:07.655 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:46:07.655 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:46:07.664 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:46:07.664 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:46:07.664 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:46:07.664 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:46:07.665 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:46:07.665 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:46:07.665 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:46:07.665 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:46:07.665 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:46:07.671 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:46:07.672 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:46:07.674 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:46:07.674 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:46:07.674 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:46:08.677 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:46:08.677 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:46:08.677 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:46:08.677 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:46:08.677 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:46:08.677 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:46:08.678 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:46:08.682 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:46:08.682 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:46:10.684 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试队列处理和线程消费功能 ===
2025-08-23 14:46:10.783 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列处理统计:
2025-08-23 14:46:10.783 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列0: 处理13个请求
2025-08-23 14:46:10.783 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列1: 处理7个请求
2025-08-23 14:46:10.783 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列2: 处理0个请求
2025-08-23 14:46:10.783 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列3: 处理0个请求
2025-08-23 14:46:10.783 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列处理和线程消费功能测试通过 ✓
2025-08-23 14:46:10.784 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:46:10.784 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:46:10.784 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:46:10.791 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:46:10.791 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:46:10.791 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:46:10.792 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:46:10.792 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:46:11.799 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:46:11.799 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:46:11.799 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:46:11.799 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:46:11.799 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:46:11.799 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:46:11.800 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:46:11.804 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:46:11.804 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:46:13.810 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试透明模式发包功能 ===
2025-08-23 14:46:13.810 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 创建透明代理请求: target=httpbin.org:80, transparent=true
2025-08-23 14:46:13.810 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 透明模式发包功能测试通过 ✓
2025-08-23 14:46:13.810 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:46:13.810 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:46:13.810 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:46:13.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:46:13.825 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:46:13.825 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:46:13.826 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:46:13.826 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:46:13.826 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:46:14.820 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:46:14.820 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:46:14.820 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:46:14.820 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:46:14.820 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:46:14.820 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:46:14.820 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:46:14.820 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:46:14.820 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:14.820 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:14.821 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:46:14.821 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:46:14.821 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:46:14.821 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:46:14.821 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:46:14.821 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:46:14.824 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:46:14.824 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:46:14.824 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:46:14.824 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:46:14.824 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:46:14.825 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:46:14.825 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:46:14.825 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:46:14.825 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:46:14.825 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:46:14.825 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:46:16.835 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试Relay连接建立功能 ===
2025-08-23 14:46:16.840 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 创建relay请求: requestId=relay-test-001, target=httpbin.org:80, protocol=HTTP
2025-08-23 14:46:16.846 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - Relay请求包解析正确: [relay-test-001, HTTP, httpbin.org, 80]
2025-08-23 14:46:16.846 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - Relay连接建立功能测试通过 ✓
2025-08-23 14:46:16.846 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:46:16.846 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:46:16.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:46:16.850 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:46:16.852 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:46:16.852 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:46:16.852 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:46:16.852 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:46:17.857 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:46:17.857 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:46:17.857 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:46:17.857 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:46:17.857 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:46:17.857 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:46:17.857 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:46:17.857 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:46:17.858 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:46:17.861 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:46:17.862 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:46:17.862 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:46:19.863 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试端到端集成功能 ===
2025-08-23 14:46:19.864 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-0 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-2] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-1 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-3 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-2] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-4 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-5 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-2] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-6 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-7 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-2] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-9 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-3] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-2 处理完成
2025-08-23 14:46:19.865 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端请求 e2e-test-8 处理完成
2025-08-23 14:46:19.865 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 端到端集成功能测试通过 ✓
2025-08-23 14:46:19.866 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:46:19.866 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:46:19.866 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:46:19.878 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:46:19.878 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:46:19.879 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:46:19.879 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:46:19.879 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:46:20.870 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:46:20.870 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:46:20.870 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:46:20.870 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:46:20.870 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:46:20.870 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:46:20.870 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:46:20.870 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:46:20.871 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:46:20.874 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:46:20.875 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:46:20.875 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:46:20.875 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:46:22.884 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试性能和稳定性 ===
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 性能测试结果:
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 总请求数: 100
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 成功请求数: 100
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 失败请求数: 0
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 成功率: {:.2f}%
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 耗时: 7ms
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - QPS: {:.2f}
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 性能和稳定性测试通过 ✓
2025-08-23 14:46:22.891 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:46:22.892 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:46:22.892 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:46:22.901 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:46:22.901 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:46:22.902 [ForkJoinPool.commonPool-worker-4] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:46:22.902 [ForkJoinPool.commonPool-worker-4] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:46:22.902 [ForkJoinPool.commonPool-worker-4] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:46:23.911 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:46:23.912 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:46:54.740 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:46:54.744 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:46:54.748 [main] WARN  c.x.p.s.c.ProxyServerV2ConfigManager - 配置目录中未找到YAML配置文件: /app/config/
2025-08-23 14:46:54.750 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用properties文件指定的配置文件: netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml
2025-08-23 14:46:54.750 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 未找到 YAML 配置文件，使用默认配置
2025-08-23 14:46:54.758 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用默认配置初始化
2025-08-23 14:46:54.758 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 配置加载完成 - 认证: 禁用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
2025-08-23 14:46:54.758 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 连接池配置 - 最大连接数/主机: 20, 空闲超时: 30秒, 清理间隔: 30秒
2025-08-23 14:46:54.758 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 性能监控配置 - 报告间隔: 30秒
2025-08-23 14:46:54.758 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 黑名单配置 - 失败阈值: 3, 缓存超时: 30秒
2025-08-23 14:46:54.763 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:46:54.765 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:46:54.765 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:46:54.771 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:46:54.771 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:46:54.772 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:46:54.773 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:54.773 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:46:54.774 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:46:54.774 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:46:54.774 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:46:54.774 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:46:54.774 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:46:54.774 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:46:54.785 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:46:54.785 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:46:54.785 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:46:54.785 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:46:54.785 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:46:54.786 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:46:54.788 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:46:54.788 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:46:54.790 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:46:54.790 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:46:54.790 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:46:56.803 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试Hash分配包到队列功能 ===
2025-08-23 14:46:56.804 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 第1轮分配测试
2025-08-23 14:46:56.807 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 第2轮分配测试
2025-08-23 14:46:56.808 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 第3轮分配测试
2025-08-23 14:46:56.808 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列分布统计:
2025-08-23 14:46:56.809 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列0: 18个连接键值
2025-08-23 14:46:56.809 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列1: 18个连接键值
2025-08-23 14:46:56.809 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列2: 14个连接键值
2025-08-23 14:46:56.809 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列3: 14个连接键值
2025-08-23 14:46:56.809 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - Hash分配包到队列功能测试通过 ✓
2025-08-23 14:46:56.811 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:46:56.811 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:46:56.811 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:46:56.820 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:46:56.820 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:46:56.820 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:46:56.820 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:46:56.821 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:46:56.821 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:46:56.821 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:46:56.821 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:46:56.821 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:46:56.834 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:46:56.839 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:46:56.840 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:46:56.840 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:46:56.840 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:46:57.828 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:46:57.829 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:47:45.536 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:47:45.540 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:47:45.544 [main] WARN  c.x.p.s.c.ProxyServerV2ConfigManager - 配置目录中未找到YAML配置文件: /app/config/
2025-08-23 14:47:45.547 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用properties文件指定的配置文件: netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml
2025-08-23 14:47:45.547 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 未找到 YAML 配置文件，使用默认配置
2025-08-23 14:47:45.555 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用默认配置初始化
2025-08-23 14:47:45.555 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 配置加载完成 - 认证: 禁用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
2025-08-23 14:47:45.555 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 连接池配置 - 最大连接数/主机: 20, 空闲超时: 30秒, 清理间隔: 30秒
2025-08-23 14:47:45.555 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 性能监控配置 - 报告间隔: 30秒
2025-08-23 14:47:45.555 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 黑名单配置 - 失败阈值: 3, 缓存超时: 30秒
2025-08-23 14:47:45.561 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:47:45.562 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:47:45.562 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:47:45.569 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:47:45.569 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:47:45.570 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:47:45.571 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:47:45.572 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:47:45.572 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:47:45.572 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:47:45.572 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:47:45.572 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:47:45.572 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:47:45.572 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:47:45.585 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:47:45.585 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:47:45.585 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:47:45.585 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:47:45.585 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:47:45.586 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:47:45.590 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:47:45.590 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:47:45.592 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:47:45.593 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:47:45.593 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:47:47.608 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试队列处理和线程消费功能 ===
2025-08-23 14:47:47.702 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列处理统计:
2025-08-23 14:47:47.703 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列0: 处理13个请求
2025-08-23 14:47:47.704 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列1: 处理7个请求
2025-08-23 14:47:47.704 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列2: 处理0个请求
2025-08-23 14:47:47.704 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列3: 处理0个请求
2025-08-23 14:47:47.704 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 队列处理和线程消费功能测试通过 ✓
2025-08-23 14:47:47.706 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:47:47.706 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:47:47.707 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:47:47.715 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:47:47.731 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:47:47.732 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:47:47.733 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:47:47.733 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:47:47.733 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:47:48.720 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:47:48.721 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:48:44.412 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:48:44.416 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:48:44.421 [main] WARN  c.x.p.s.c.ProxyServerV2ConfigManager - 配置目录中未找到YAML配置文件: /app/config/
2025-08-23 14:48:44.422 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用properties文件指定的配置文件: netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml
2025-08-23 14:48:44.422 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 未找到 YAML 配置文件，使用默认配置
2025-08-23 14:48:44.431 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用默认配置初始化
2025-08-23 14:48:44.431 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 配置加载完成 - 认证: 禁用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
2025-08-23 14:48:44.431 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 连接池配置 - 最大连接数/主机: 20, 空闲超时: 30秒, 清理间隔: 30秒
2025-08-23 14:48:44.431 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 性能监控配置 - 报告间隔: 30秒
2025-08-23 14:48:44.431 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 黑名单配置 - 失败阈值: 3, 缓存超时: 30秒
2025-08-23 14:48:44.436 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:48:44.438 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:48:44.438 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:48:44.444 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:48:44.444 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:48:44.444 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:48:44.446 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:48:44.457 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:48:44.457 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:48:44.457 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:48:44.457 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:48:44.457 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:48:44.458 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:48:44.461 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:48:44.461 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:48:44.463 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:48:44.463 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:48:44.463 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:48:46.468 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试透明模式发包功能 ===
2025-08-23 14:48:46.561 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 创建透明代理请求: target=httpbin.org:80, transparent=true
2025-08-23 14:48:46.561 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 透明模式发包功能测试通过 ✓
2025-08-23 14:48:46.562 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:48:46.562 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:48:46.563 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:48:46.571 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:48:46.571 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:48:46.571 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:48:46.571 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:48:46.572 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:48:46.572 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:48:46.572 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:48:46.572 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:48:46.572 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:48:46.575 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:48:46.576 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:48:46.577 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:48:46.577 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:48:46.578 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:48:47.575 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:48:47.575 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
2025-08-23 14:49:45.637 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 设置测试环境...
2025-08-23 14:49:45.642 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 14:49:45.647 [main] WARN  c.x.p.s.c.ProxyServerV2ConfigManager - 配置目录中未找到YAML配置文件: /app/config/
2025-08-23 14:49:45.648 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用properties文件指定的配置文件: netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml
2025-08-23 14:49:45.649 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 未找到 YAML 配置文件，使用默认配置
2025-08-23 14:49:45.660 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用默认配置初始化
2025-08-23 14:49:45.660 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 配置加载完成 - 认证: 禁用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
2025-08-23 14:49:45.660 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 连接池配置 - 最大连接数/主机: 20, 空闲超时: 30秒, 清理间隔: 30秒
2025-08-23 14:49:45.660 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 性能监控配置 - 报告间隔: 30秒
2025-08-23 14:49:45.660 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 黑名单配置 - 失败阈值: 3, 缓存超时: 30秒
2025-08-23 14:49:45.665 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 14:49:45.666 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 14:49:45.667 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 14:49:45.673 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 14:49:45.674 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 14:49:45.674 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 14:49:45.675 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=internal-direct, name=内网直连, priority=10, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:49:45.676 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=local-direct, name=本地域名直连, priority=20, outbound=direct, enabled=true, matchers=1}
2025-08-23 14:49:45.676 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=default-direct, name=默认路由, priority=999, outbound=direct, enabled=true, matchers=0}
2025-08-23 14:49:45.676 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置3条规则
2025-08-23 14:49:45.676 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 14:49:45.676 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 14:49:45.676 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 14:49:45.676 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 14:49:45.687 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 14:49:45.687 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 14:49:45.687 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 14:49:45.688 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 14:49:45.688 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共0个
2025-08-23 14:49:45.688 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 14:49:45.690 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=0, running=0, stopped=0, connections=0}
2025-08-23 14:49:45.691 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 14:49:45.692 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每30秒输出一次统计信息
2025-08-23 14:49:45.692 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 14:49:45.693 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - ProxyServer启动成功
2025-08-23 14:49:47.699 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - === 测试透明模式发包功能 ===
2025-08-23 14:49:47.788 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 创建透明代理请求: target=httpbin.org:80, transparent=true
2025-08-23 14:49:47.792 [main] INFO  c.x.p.s.i.ProxyServerIntegrationTest - 清理测试环境...
2025-08-23 14:49:47.792 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止代理服务器V2...
2025-08-23 14:49:47.792 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止监控服务...
2025-08-23 14:49:47.800 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.u.ThreadPoolPerformanceAnalyzer - 线程池性能监控已停止
2025-08-23 14:49:47.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务已停止
2025-08-23 14:49:47.800 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 停止所有Inbound服务器...
2025-08-23 14:49:47.800 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 停止所有Inbound服务器，共0个
2025-08-23 14:49:47.801 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器停止完成
2025-08-23 14:49:47.801 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 所有Inbound服务器已停止
2025-08-23 14:49:47.801 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 开始关闭代理处理器...
2025-08-23 14:49:47.801 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 等待队列中的请求处理完成...
2025-08-23 14:49:47.801 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭工作线程...
2025-08-23 14:49:47.806 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 关闭活跃连接...
2025-08-23 14:49:47.808 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 销毁处理器...
2025-08-23 14:49:47.809 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 代理处理器关闭完成，总处理请求数: 0
2025-08-23 14:49:47.809 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 清空所有路由规则，共清空 3 条规则
2025-08-23 14:49:47.809 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2停止完成
2025-08-23 14:49:48.814 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 收到关闭信号，正在优雅关闭代理服务器V2...
2025-08-23 14:49:48.814 [ProxyServerV2-Shutdown] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2优雅关闭完成
